using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Cysharp.Threading.Tasks;

namespace LevelEditor.Services
{
    public class BonusWordsDetectionService
    {
        private readonly WordsData _wordsData;
        private readonly HashSet<string> _allValidWords;
        private readonly Dictionary<int, HashSet<string>> _wordsByLength; // Группировка слов по длине
        private readonly HashSet<string> _wordPrefixes; // Префиксы для быстрой проверки
        private readonly Dictionary<string, List<WordPathInfo>> _matrixCache; // Кэш результатов для матриц
        private int _operationCounter = 0;
        private const int OPERATIONS_PER_YIELD = 500; // Увеличено для лучшей производительности

        // Внешние сервисы валидации
        private readonly List<IWordValidationService> _externalServices;
        private readonly ValidationServiceSettings _settings;

        public BonusWordsDetectionService() : this(new ValidationServiceSettings())
        {
        }

        public BonusWordsDetectionService(ValidationServiceSettings settings)
        {
            _settings = settings ?? new ValidationServiceSettings();
            _wordsData = UnityEditor.AssetDatabase.LoadAssetAtPath<WordsData>("Assets/_Project/Prefabs/SO/tables/WordsData.asset");
            _allValidWords = new HashSet<string>();
            _wordsByLength = new Dictionary<int, HashSet<string>>();
            _wordPrefixes = new HashSet<string>();
            _matrixCache = new Dictionary<string, List<WordPathInfo>>();
            _externalServices = new List<IWordValidationService>();

            // Инициализируем локальную базу слов
            InitializeLocalWordDatabase();

            // Инициализируем внешние сервисы
            InitializeExternalServices();
        }

        private void InitializeLocalWordDatabase()
        {
            if (_wordsData != null)
            {
                // Загружаем все слова из WordsData и создаем оптимизированные структуры
                foreach (var wordsSet in _wordsData.WordsSets)
                {
                    foreach (var word in wordsSet.WordsArray)
                    {
                        string upperWord = word.ToUpper();
                        _allValidWords.Add(upperWord);

                        // Группируем по длине для быстрого поиска
                        int length = upperWord.Length;
                        if (!_wordsByLength.ContainsKey(length))
                            _wordsByLength[length] = new HashSet<string>();
                        _wordsByLength[length].Add(upperWord);

                        // Создаем префиксы для быстрой проверки возможности продолжения
                        for (int i = 1; i <= upperWord.Length; i++)
                        {
                            _wordPrefixes.Add(upperWord.Substring(0, i));
                        }
                    }
                }
                Debug.Log($"✅ Loaded {_allValidWords.Count} local words, {_wordPrefixes.Count} prefixes for optimized bonus detection");
            }
            else
            {
                Debug.LogWarning("WordsData not found! Only external services will be used for word validation.");
            }
        }

        private void InitializeExternalServices()
        {
            // Добавляем Yandex Dictionary сервис
            if (_settings.UseYandexDictionary && !string.IsNullOrEmpty(_settings.YandexApiKey))
            {
                var yandexService = new YandexDictionaryService(_settings.YandexApiKey, _settings.RequestTimeoutSeconds, _settings);
                _externalServices.Add(yandexService);
                Debug.Log("✅ Yandex Dictionary service initialized with filtering settings");
            }

            // Добавляем OpenAI сервис
            if (_settings.UseOpenAI && !string.IsNullOrEmpty(_settings.OpenAIApiKey))
            {
                var openAIService = new OpenAIWordService(_settings.OpenAIApiKey, _settings.RequestTimeoutSeconds);
                _externalServices.Add(openAIService);
                Debug.Log("✅ OpenAI service initialized");
            }

            Debug.Log($"🔧 Initialized {_externalServices.Count} external validation services");
        }
        
        /// <summary>
        /// Находит все бонусные слова в матрице уровня
        /// </summary>
        public async UniTask<List<BonusWordInfo>> FindBonusWordsAsync(LevelsSO level, IProgress<float> progress = null)
        {
            var bonusWords = new List<BonusWordInfo>();

            if (level.FillMatrixes == null || level.FillMatrixes.Count == 0)
            {
                Debug.LogWarning($"⚠️ Level {level.LevelNum} has no matrices");
                return bonusWords;
            }

            var mainWords = new HashSet<string>(level.Words.Select(w => w.ToUpper()));
            Debug.Log($"🎯 Level {level.LevelNum}: Optimized search in {level.FillMatrixes.Count} matrices. Main words: {string.Join(", ", mainWords)}");

            for (int matrixIndex = 0; matrixIndex < level.FillMatrixes.Count; matrixIndex++)
            {
                float matrixProgress = (float)matrixIndex / level.FillMatrixes.Count;
                progress?.Report(matrixProgress);

                Debug.Log($"🔍 Processing matrix {matrixIndex + 1}/{level.FillMatrixes.Count} for Level {level.LevelNum}");

                var matrix = level.FillMatrixes[matrixIndex];
                var foundWords = await FindAllWordsInMatrixAsync(matrix);

                int bonusWordsInMatrix = 0;

                // Фильтруем только бонусные слова (не основные)
                var candidateWords = new List<string>();
                var localValidWords = new List<WordPathInfo>();

                foreach (var wordInfo in foundWords)
                {
                    if (!mainWords.Contains(wordInfo.Word))
                    {
                        // Сначала проверяем локальную базу
                        if (_settings.UseLocalDictionary && _allValidWords.Contains(wordInfo.Word))
                        {
                            localValidWords.Add(wordInfo);
                        }
                        else if (_externalServices.Count > 0)
                        {
                            // Добавляем в список для проверки внешними сервисами
                            candidateWords.Add(wordInfo.Word);
                        }
                    }
                }

                // Добавляем локально найденные слова
                foreach (var wordInfo in localValidWords)
                {
                    bonusWords.Add(new BonusWordInfo
                    {
                        Word = wordInfo.Word,
                        MatrixIndex = matrixIndex,
                        Path = wordInfo.Path,
                        Length = wordInfo.Word.Length,
                        Source = "Local"
                    });
                    bonusWordsInMatrix++;
                }

                // Проверяем кандидатов внешними сервисами
                if (candidateWords.Count > 0)
                {
                    var externalValidWords = await ValidateWordsWithExternalServices(candidateWords);

                    foreach (var wordInfo in foundWords)
                    {
                        if (candidateWords.Contains(wordInfo.Word) && externalValidWords.ContainsKey(wordInfo.Word) && externalValidWords[wordInfo.Word])
                        {
                            bonusWords.Add(new BonusWordInfo
                            {
                                Word = wordInfo.Word,
                                MatrixIndex = matrixIndex,
                                Path = wordInfo.Path,
                                Length = wordInfo.Word.Length,
                                Source = "External"
                            });
                            bonusWordsInMatrix++;
                        }
                    }
                }

                Debug.Log($"✅ Matrix {matrixIndex + 1}: Found {bonusWordsInMatrix} bonus words");

                // Пауза между матрицами
                await UniTask.Yield();
            }

            progress?.Report(1f);
            var result = bonusWords.OrderBy(w => w.Word).ToList();
            Debug.Log($"🎉 Level {level.LevelNum}: Total {result.Count} unique bonus words found");
            return result;
        }
        
        /// <summary>
        /// Находит все возможные слова в матрице (оптимизированная версия с кэшированием)
        /// </summary>
        private async UniTask<List<WordPathInfo>> FindAllWordsInMatrixAsync(FillMatrix matrix)
        {
            // Создаем ключ для кэширования на основе содержимого матрицы
            string matrixKey = GenerateMatrixKey(matrix);

            // Проверяем кэш
            if (_matrixCache.ContainsKey(matrixKey))
            {
                Debug.Log($"🚀 Cache hit for matrix {matrix.RowCount}x{matrix.ColCount}");
                return _matrixCache[matrixKey];
            }

            var foundWords = new HashSet<string>(); // Используем HashSet для избежания дубликатов
            var wordPaths = new Dictionary<string, List<Vector2Int>>(); // Сохраняем пути для найденных слов
            _operationCounter = 0;

            Debug.Log($"🔍 Optimized search in {matrix.RowCount}x{matrix.ColCount} matrix...");

            // Используем итеративный подход вместо рекурсии для лучшей производительности
            for (int startRow = 0; startRow < matrix.RowCount; startRow++)
            {
                for (int startCol = 0; startCol < matrix.ColCount; startCol++)
                {
                    await FindWordsFromPositionIterativeAsync(matrix, startRow, startCol, foundWords, wordPaths);

                    // Периодические паузы
                    _operationCounter++;
                    if (_operationCounter % OPERATIONS_PER_YIELD == 0)
                    {
                        await UniTask.Yield();
                    }
                }

                // Пауза после каждой строки
                if (startRow % 2 == 0)
                    await UniTask.Yield();
            }

            // Конвертируем результат
            var result = foundWords.Select(word => new WordPathInfo
            {
                Word = word,
                Path = wordPaths.ContainsKey(word) ? wordPaths[word] : new List<Vector2Int>()
            }).ToList();

            // Сохраняем в кэш
            _matrixCache[matrixKey] = result;

            Debug.Log($"✅ Found {result.Count} unique words in matrix (cached)");
            return result;
        }

        /// <summary>
        /// Генерирует ключ для кэширования матрицы
        /// </summary>
        private string GenerateMatrixKey(FillMatrix matrix)
        {
            var key = new System.Text.StringBuilder();
            for (int r = 0; r < matrix.RowCount; r++)
            {
                for (int c = 0; c < matrix.ColCount; c++)
                {
                    key.Append(matrix[r, c].GetCharLetter());
                }
            }
            return key.ToString();
        }
        
        /// <summary>
        /// Итеративно ищет слова начиная с определенной позиции (оптимизированная версия)
        /// </summary>
        private async UniTask FindWordsFromPositionIterativeAsync(FillMatrix matrix, int startRow, int startCol,
            HashSet<string> foundWords, Dictionary<string, List<Vector2Int>> wordPaths)
        {
            var directions = new Vector2Int[]
            {
                new(-1, 0),  // Вверх
                new(1, 0),   // Вниз
                new(0, -1),  // Влево
                new(0, 1)    // Вправо
            };

            // Используем стек для итеративного обхода вместо рекурсии
            var stack = new Stack<SearchState>();
            stack.Push(new SearchState
            {
                Row = startRow,
                Col = startCol,
                Path = new List<Vector2Int> { new Vector2Int(startRow, startCol) },
                Word = matrix[startRow, startCol].GetCharLetter().ToString(),
                Visited = new bool[matrix.RowCount, matrix.ColCount]
            });

            int iterations = 0;
            const int MAX_ITERATIONS_PER_YIELD = 1000;

            while (stack.Count > 0)
            {
                var state = stack.Pop();
                state.Visited[state.Row, state.Col] = true;

                // Ранняя проверка префикса - если текущее слово не может быть началом валидного слова, пропускаем
                if (state.Word.Length > 1 && !_wordPrefixes.Contains(state.Word.ToUpper()))
                {
                    continue;
                }

                // Проверяем, является ли текущее слово валидным
                // Используем минимальную длину из настроек внешних сервисов для всех проверок
                int minLength = _settings.MinWordLengthForExternal;
                int maxLength = _settings.MaxWordLengthForExternal;

                if (state.Word.Length >= minLength && state.Word.Length <= maxLength)
                {
                    string upperWord = state.Word.ToUpper();

                    // Для локальной базы проверяем сразу
                    if (_settings.UseLocalDictionary && _allValidWords.Contains(upperWord) && !foundWords.Contains(upperWord))
                    {
                        foundWords.Add(upperWord);
                        wordPaths[upperWord] = new List<Vector2Int>(state.Path);
                    }
                    // Для внешних сервисов добавляем все кандидаты (проверим позже)
                    else if (_externalServices.Count > 0 && !foundWords.Contains(upperWord))
                    {
                        foundWords.Add(upperWord);
                        wordPaths[upperWord] = new List<Vector2Int>(state.Path);
                    }
                }

                // Продолжаем поиск только если слово не слишком длинное
                if (state.Word.Length < 8)
                {
                    foreach (var dir in directions)
                    {
                        int newRow = state.Row + dir.x;
                        int newCol = state.Col + dir.y;

                        if (IsValidPosition(newRow, newCol, matrix.RowCount, matrix.ColCount) &&
                            !state.Visited[newRow, newCol])
                        {
                            var newPath = new List<Vector2Int>(state.Path);
                            newPath.Add(new Vector2Int(newRow, newCol));

                            var newVisited = (bool[,])state.Visited.Clone();

                            stack.Push(new SearchState
                            {
                                Row = newRow,
                                Col = newCol,
                                Path = newPath,
                                Word = state.Word + matrix[newRow, newCol].GetCharLetter(),
                                Visited = newVisited
                            });
                        }
                    }
                }

                // Периодические паузы для отзывчивости UI
                iterations++;
                if (iterations % MAX_ITERATIONS_PER_YIELD == 0)
                {
                    await UniTask.Yield();
                }
            }
        }

        private bool IsValidPosition(int row, int col, int maxRows, int maxCols)
        {
            return row >= 0 && row < maxRows && col >= 0 && col < maxCols;
        }

        private class SearchState
        {
            public int Row;
            public int Col;
            public List<Vector2Int> Path;
            public string Word;
            public bool[,] Visited;
        }

        /// <summary>
        /// Проверяет, что путь является валидной змейкой (только соседние клетки по 4 направлениям)
        /// </summary>
        private bool IsValidSnakePath(List<Vector2Int> path)
        {
            if (path.Count < 2) return true;

            for (int i = 1; i < path.Count; i++)
            {
                var prev = path[i - 1];
                var curr = path[i];

                // Вычисляем разность координат
                int deltaRow = Mathf.Abs(curr.x - prev.x);
                int deltaCol = Mathf.Abs(curr.y - prev.y);

                // Валидный ход: либо по горизонтали, либо по вертикали на 1 клетку
                bool isValidMove = (deltaRow == 1 && deltaCol == 0) || (deltaRow == 0 && deltaCol == 1);

                if (!isValidMove)
                {
                    return false; // Найден невалидный ход (диагональ или прыжок)
                }
            }

            return true;
        }

        /// <summary>
        /// Очищает кэш для экономии памяти
        /// </summary>
        public void ClearCache()
        {
            int cacheSize = _matrixCache.Count;
            _matrixCache.Clear();
            Debug.Log($"🧹 Matrix cache cleared ({cacheSize} entries removed)");
        }

        /// <summary>
        /// Валидирует слова с помощью внешних сервисов
        /// </summary>
        private async UniTask<Dictionary<string, bool>> ValidateWordsWithExternalServices(List<string> words)
        {
            var results = new Dictionary<string, bool>();

            if (_externalServices.Count == 0 || words.Count == 0)
                return results;

            Debug.Log($"🔍 Validating {words.Count} words with {_externalServices.Count} external services...");

            foreach (string word in words)
            {
                results[word] = false; // По умолчанию невалидно

                // Проверяем каждым сервисом до первого положительного результата
                foreach (var service in _externalServices)
                {
                    try
                    {
                        bool isValid = await service.IsValidWordAsync(word);
                        if (isValid)
                        {
                            results[word] = true;
                            Debug.Log($"✅ Word '{word}' validated by {service.ServiceName}");
                            break; // Найдено валидное слово, переходим к следующему
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogWarning($"⚠️ Error validating '{word}' with {service.ServiceName}: {ex.Message}");
                    }

                    // Небольшая задержка между запросами
                    await UniTask.Delay(100);
                }
            }

            int validCount = results.Count(kv => kv.Value);
            Debug.Log($"🎯 External validation complete: {validCount}/{words.Count} words validated");

            return results;
        }

        /// <summary>
        /// Получает статистику загруженных данных
        /// </summary>
        public string GetStatistics()
        {
            var stats = $"Local words: {_allValidWords.Count}, Prefixes: {_wordPrefixes.Count}, " +
                       $"Length groups: {_wordsByLength.Count}, Matrix cache: {_matrixCache.Count}";

            if (_externalServices.Count > 0)
            {
                stats += $", External services: {_externalServices.Count}";
                foreach (var service in _externalServices)
                {
                    stats += $"\n  - {service.ServiceName}: {service.GetCacheStatistics()}";
                }
            }

            return stats;
        }

        /// <summary>
        /// Очищает все кэши
        /// </summary>
        public void ClearAllCaches()
        {
            ClearCache();
            foreach (var service in _externalServices)
            {
                service.ClearCache();
            }
        }
    }
    
    [System.Serializable]
    public class BonusWordInfo
    {
        public string Word;
        public int MatrixIndex;
        public List<Vector2Int> Path;
        public int Length;
        public string Source; // "Local", "External", или название сервиса

        public override string ToString() => $"{Word} (Matrix {MatrixIndex}, Length {Length}, Source: {Source})";
    }
    
    [System.Serializable]
    public class WordPathInfo
    {
        public string Word;
        public List<Vector2Int> Path;
    }
}

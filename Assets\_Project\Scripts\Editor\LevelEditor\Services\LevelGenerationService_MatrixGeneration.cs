using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Cysharp.Threading.Tasks;
using Random = UnityEngine.Random;

namespace LevelEditor.Services
{
    public partial class LevelGenerationService
    {
        private async UniTask<GenerationResult> GenerateLevelNTimesAsync(LevelsSO level, int n)
        {
            var result = new GenerationResult();
            level.FillMatrixes = new List<FillMatrix>();

            for (int i = 0; i < n; i++)
            {
                bool success = false;
                bool isFirstMatrix = (i == 0);

                // Store current matrix index for use in generation algorithms
                currentMatrixIndex = i;

                // For first matrix, use stricter attempts and validation
                int attemptsForThisMatrix = isFirstMatrix ? maxAttemptsPerMatrix * 2 : maxAttemptsPerMatrix;

                for (int attempt = 0; attempt < attemptsForThisMatrix; attempt++)
                {
                    currentMatrix = GenerateNewMatrixForLevel(level, out bool isFull);

                    if (!isFull)
                    {
                        continue;
                    }

                    if (!IsMatrixValid(currentMatrix, level, out string failReason, out string failWord))
                    {
                        continue;
                    }

                    // Additional strict validation ONLY for first matrix (if enabled)
                    if (isFirstMatrix && LevelGenerationService.StrictFirstMatrix && !IsFirstMatrixValid(currentMatrix, level))
                    {
                        continue;
                    }

                    FillMatrix fillMatrix = new FillMatrix(level.RowsCount, level.ColumnsCount);

                    for (int r = 0; r < level.RowsCount; r++)
                    {
                        for (int c = 0; c < level.ColumnsCount; c++)
                        {
                            fillMatrix[r, c] = new LetterData(
                                currentMatrix[r, c].ToString(),
                                letterInfos[r, c]?.Word ?? "",
                                letterInfos[r, c]?.IndexInWord ?? -1
                            );
                        }
                    }
                    level.FillMatrixes.Add(fillMatrix);

                    success = true;
                    break;
                }

                if (!success)
                {
                    string matrixType = isFirstMatrix ? "first matrix (primary)" : $"matrix {i}";
                    result.Warnings.Add($"Failed to generate {matrixType} for level {level.LevelNum} after {attemptsForThisMatrix} attempts");
                }

                await UniTask.Delay(1);
            }

            return result;
        }

        private char[,] GenerateNewMatrixForLevel(LevelsSO level, out bool allWordsPlaced)
        {
            letterInfos = new LetterInfo[level.RowsCount, level.ColumnsCount];
            currentMatrix = new char[level.RowsCount, level.ColumnsCount];
            emptyCells.Clear();
            currentCells.Clear();
            FillMatrixWithEmptyCells();

            allWordsPlaced = true;
            foreach (string word in level.Words)
            {
                bool isReversed = RollChance(level.BackwardsProbability);
                if (!TryAddWord(word, isReversed))
                {
                    allWordsPlaced = false;
                    break;
                }
            }

            return currentMatrix;
        }

        private static bool RollChance(int chancePercent)
        {
            chancePercent = Mathf.Clamp(chancePercent, 0, 100);
            return Random.Range(0, 100) < chancePercent;
        }

        private void FillMatrixWithEmptyCells()
        {
            for (int i = 0; i < currentMatrix.GetLength(0); i++)
            {
                for (int j = 0; j < currentMatrix.GetLength(1); j++)
                {
                    currentMatrix[i, j] = '0';
                    emptyCells.Add(new Vector2Int(i, j));
                }
            }
        }

        private bool TryAddWord(string word, bool isReversed)
        {
            Shuffle(emptyCells);
            foreach (Vector2Int cell in new List<Vector2Int>(emptyCells))
            {
                AddLetterToCell(word[0], cell, word, 0);
                if (TryAddWordRecursive(cell, word, 1, isReversed))
                    return true;

                ClearLastCell();
            }

            return false;
        }

        private bool TryAddWordRecursive(Vector2Int currentPos, string word, int index, bool isReversed)
        {
            if (index == word.Length)
            {
                // Check straight lines only for FIRST matrix and if both prevention and straight line check are enabled
                bool isFirstMatrix = (currentMatrixIndex == 0);
                if (isFirstMatrix && LevelGenerationService.PreventStraightLines && LevelGenerationService.EnableStraightLineCheck)
                {
                    // In fast mode, only check very long words (5+) for straight lines
                    // In normal mode, check words 4+ letters
                    int minWordLength = LevelGenerationService.FastGeneration ? 5 : 4;

                    if (word.Length >= minWordLength && CanWordBeCollectedInStraightLine(word))
                    {
                        return false; // Reject words that can be collected in straight lines
                    }
                }
                return true;
            }

            // Get available directions with smart bend prioritization
            var availableDirections = GetAvailableDirections(currentPos, isReversed, index);

            foreach (Vector2Int dir in availableDirections)
            {
                Vector2Int newPos = currentPos + dir;
                if (IsCellInside(newPos) && IsCellEmpty(newPos))
                {
                    AddLetterToCell(word[index], newPos, word, index);
                    if (TryAddWordRecursive(newPos, word, index + 1, isReversed))
                        return true;

                    ClearLastCell();
                }
            }

            return false;
        }

        private void Shuffle<T>(IList<T> list)
        {
            for (int i = list.Count - 1; i > 0; i--)
            {
                int j = Random.Range(0, i + 1);
                (list[i], list[j]) = (list[j], list[i]);
            }
        }

        private void Shuffle(Vector2Int[] array)
        {
            for (int i = array.Length - 1; i > 0; i--)
            {
                int j = Random.Range(0, i + 1);
                (array[i], array[j]) = (array[j], array[i]);
            }
        }

        private void AddLetterToCell(char letter, Vector2Int cell, string word, int indexInWord)
        {
            currentMatrix[cell.x, cell.y] = letter;
            letterInfos[cell.x, cell.y] = new LetterInfo(word, indexInWord);
            currentCells.Push(cell);
            emptyCells.Remove(cell);
        }

        private void ClearLastCell()
        {
            Vector2Int cell = currentCells.Pop();
            currentMatrix[cell.x, cell.y] = '0';
            emptyCells.Add(cell);
        }

        private bool CanWordBeCollectedInStraightLine(string word)
        {
            // Get the positions of the word letters from currentCells stack
            var wordPositions = new List<Vector2Int>();
            var tempStack = new Stack<Vector2Int>();

            // Extract positions from stack (they are in reverse order)
            while (currentCells.Count > 0)
            {
                var pos = currentCells.Pop();
                tempStack.Push(pos);
                wordPositions.Add(pos);
            }

            // Restore the stack
            while (tempStack.Count > 0)
            {
                currentCells.Push(tempStack.Pop());
            }

            // Reverse to get correct order (first letter first)
            wordPositions.Reverse();

            if (wordPositions.Count < 2)
                return false;

            // Check if all positions form a straight line
            Vector2Int direction = wordPositions[1] - wordPositions[0];

            for (int i = 2; i < wordPositions.Count; i++)
            {
                Vector2Int currentDirection = wordPositions[i] - wordPositions[i - 1];
                if (currentDirection != direction)
                {
                    return false; // Not a straight line - has turns/bends
                }
            }

            return true; // All positions are in a straight line
        }

        private List<Vector2Int> GetAvailableDirections(Vector2Int currentPos, bool isReversed, int index)
        {
            var directions = new List<Vector2Int>(Directions.directions);

            // Apply reversed/forward restrictions
            directions.RemoveAll(dir =>
            {
                if (isReversed && index == 1 && (dir == Directions.Right || dir == Directions.Down))
                    return true;
                if (!isReversed && (dir == Directions.Left || dir == Directions.Up))
                    return true;
                return false;
            });

            // Apply bend prioritization ONLY to first matrix
            bool isFirstMatrix = (currentMatrixIndex == 0);
            if (isFirstMatrix && LevelGenerationService.PreventStraightLines && LevelGenerationService.EnableStraightLineCheck)
            {
                if (LevelGenerationService.FastGeneration)
                {
                    return GetDirectionsWithFastBends(directions, index);
                }
                else
                {
                    return GetDirectionsWithForcedBends(directions, index);
                }
            }

            // Default behavior - just shuffle
            Shuffle(directions);
            return directions;
        }

        private List<Vector2Int> GetDirectionsWithForcedBends(List<Vector2Int> availableDirections, int index)
        {
            // For the first two letters, any direction is fine
            if (index <= 1 || currentCells.Count < 2)
            {
                Shuffle(availableDirections);
                return availableDirections;
            }

            // For 3+ letters, use AGGRESSIVE bend prioritization for better snake patterns
            var cellsArray = currentCells.ToArray();
            var lastPos = cellsArray[0]; // Current position
            var prevPos = cellsArray[1]; // Previous position
            var prevDirection = lastPos - prevPos;

            var bendDirections = new List<Vector2Int>();
            var straightDirections = new List<Vector2Int>();

            foreach (var dir in availableDirections)
            {
                if (dir == prevDirection)
                {
                    straightDirections.Add(dir); // Same direction = straight line
                }
                else
                {
                    bendDirections.Add(dir); // Different direction = bend
                }
            }

            // Shuffle both groups
            Shuffle(bendDirections);
            Shuffle(straightDirections);

            // AGGRESSIVE STRATEGY: Force bends much more often
            if (index >= 2) // Start forcing bends from 3rd letter
            {
                int straightSteps = CountConsecutiveStraightSteps();

                // Force bends if we have 2+ straight steps (more aggressive)
                if (straightSteps >= 2 && bendDirections.Count > 0)
                {
                    return bendDirections; // FORCE a bend to break lines early
                }

                // For words 4+ letters, heavily prefer bends
                if (index >= 3)
                {
                    float bendChance = LevelGenerationService.BendPriority * 0.8f; // Increase aggressiveness
                    if (Random.Range(0f, 1f) < bendChance && bendDirections.Count > 0)
                    {
                        return bendDirections;
                    }
                }

                // For very long words, almost always prefer bends
                if (index >= 5)
                {
                    if (Random.Range(0f, 1f) < 0.9f && bendDirections.Count > 0)
                    {
                        return bendDirections;
                    }
                }
            }

            // HEAVY prioritization: 4:1 ratio for better snake patterns
            var result = new List<Vector2Int>();
            result.AddRange(bendDirections);
            result.AddRange(bendDirections); // Add twice
            result.AddRange(bendDirections); // Add third time
            result.AddRange(bendDirections); // Add fourth time for heavy priority
            result.AddRange(straightDirections);

            return result;
        }

        private List<Vector2Int> GetDirectionsWithFastBends(List<Vector2Int> availableDirections, int index)
        {
            // Fast mode: minimal bend forcing for speed
            if (index <= 2 || currentCells.Count < 2)
            {
                Shuffle(availableDirections);
                return availableDirections;
            }

            var cellsArray = currentCells.ToArray();
            var lastPos = cellsArray[0];
            var prevPos = cellsArray[1];
            var prevDirection = lastPos - prevPos;

            var bendDirections = new List<Vector2Int>();
            var straightDirections = new List<Vector2Int>();

            foreach (var dir in availableDirections)
            {
                if (dir == prevDirection)
                {
                    straightDirections.Add(dir);
                }
                else
                {
                    bendDirections.Add(dir);
                }
            }

            Shuffle(bendDirections);
            Shuffle(straightDirections);

            // Fast mode: only force bends for very long straight lines (4+ steps)
            if (index >= 4)
            {
                int straightSteps = CountConsecutiveStraightSteps();
                if (straightSteps >= 4 && bendDirections.Count > 0)
                {
                    return bendDirections; // Force bend only for very long lines
                }
            }

            // Light prioritization: just 1.5:1 ratio for speed
            var result = new List<Vector2Int>();
            result.AddRange(bendDirections);
            result.AddRange(straightDirections);
            result.AddRange(straightDirections); // Add straight directions twice for balance

            return result;
        }

        private int CountConsecutiveStraightSteps()
        {
            if (currentCells.Count < 3) return 0;

            var cellsArray = currentCells.ToArray();
            int consecutiveSteps = 0;
            int maxConsecutive = 0;

            for (int i = 0; i < cellsArray.Length - 2; i++)
            {
                var pos1 = cellsArray[i + 2]; // Earlier position
                var pos2 = cellsArray[i + 1]; // Middle position
                var pos3 = cellsArray[i];     // Later position

                var dir1 = pos2 - pos1; // First direction
                var dir2 = pos3 - pos2; // Second direction

                if (dir1 == dir2)
                {
                    consecutiveSteps++; // Same direction = straight line continues
                    maxConsecutive = Mathf.Max(maxConsecutive, consecutiveSteps);
                }
                else
                {
                    consecutiveSteps = 0; // Reset counter on bend
                }
            }

            return maxConsecutive;
        }

        private bool HasWordGotBend()
        {
            if (currentCells.Count < 3) return false;

            var cellsArray = currentCells.ToArray();

            // Check if any consecutive directions are different (indicating a bend)
            for (int i = 0; i < cellsArray.Length - 2; i++)
            {
                var pos1 = cellsArray[i + 2]; // Earlier position
                var pos2 = cellsArray[i + 1]; // Middle position
                var pos3 = cellsArray[i];     // Later position

                var dir1 = pos2 - pos1; // First direction
                var dir2 = pos3 - pos2; // Second direction

                if (dir1 != dir2)
                {
                    return true; // Found a bend!
                }
            }

            return false; // No bends found
        }

        private bool IsFirstMatrixValid(char[,] matrix, LevelsSO level)
        {
            // First matrix must have ZERO straight line words regardless of settings
            // This ensures the primary matrix always has snake-like patterns

            foreach (string word in level.Words)
            {
                if (CanWordBeCollectedInStraightLineStrict(matrix, word))
                {
                    return false; // Reject ANY straight line words in first matrix
                }
            }

            return true;
        }

        private bool CanWordBeCollectedInStraightLineStrict(char[,] matrix, string word)
        {
            // Strict version that checks ALL words regardless of length
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);

            // Check all possible starting positions
            for (int r = 0; r < rows; r++)
            {
                for (int c = 0; c < cols; c++)
                {
                    if (matrix[r, c] == word[0])
                    {
                        // Check all 4 straight directions
                        var directions = new Vector2Int[]
                        {
                            new Vector2Int(0, 1),   // Right
                            new Vector2Int(1, 0),   // Down
                            new Vector2Int(0, -1),  // Left
                            new Vector2Int(-1, 0)   // Up
                        };

                        foreach (var direction in directions)
                        {
                            if (CheckWordInStraightDirectionStrict(matrix, word, r, c, direction))
                            {
                                return true; // Word can be collected in straight line
                            }
                        }
                    }
                }
            }

            return false; // Word cannot be collected in any straight line
        }

        private bool CheckWordInStraightDirectionStrict(char[,] matrix, string word, int startR, int startC, Vector2Int direction)
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            int r = startR;
            int c = startC;

            for (int i = 0; i < word.Length; i++)
            {
                // Check bounds
                if (r < 0 || r >= rows || c < 0 || c >= cols)
                    return false;

                // Check if letter matches
                if (matrix[r, c] != word[i])
                    return false;

                // Move to next position
                r += direction.x;
                c += direction.y;
            }

            return true; // Word found in straight line
        }
    }
}

using System;
using System.Collections.Generic;
using UnityEngine;
using Cysharp.Threading.Tasks;

namespace LevelEditor.Services
{
    public class MatrixUniquenessValidationService : BaseValidationService
    {
        public override string ServiceName => "Matrix Uniqueness Validation";
        
        protected override async UniTask ValidateInternalAsync(LevelsSO level, ValidationResult result, IProgress<ValidationProgress> progress)
        {
            if (level.FillMatrixes == null || level.FillMatrixes.Count == 0)
            {
                result.IsValid = false;
                result.Errors.Add(new ValidationError("Data", "Level has no matrices to validate"));
                return;
            }
            
            if (level.FillMatrixes.Count < 2)
            {
                // Only one matrix, so it's unique by definition
                ReportProgress(progress, "Matrix uniqueness validation completed", 1, 1);
                return;
            }
            
            int totalComparisons = (level.FillMatrixes.Count * (level.FillMatrixes.Count - 1)) / 2;
            int currentComparison = 0;
            
            ReportProgress(progress, "Starting matrix uniqueness validation", 0, totalComparisons);
            
            for (int i = 0; i < level.FillMatrixes.Count; i++)
            {
                for (int j = i + 1; j < level.FillMatrixes.Count; j++)
                {
                    currentComparison++;
                    ReportProgress(progress, $"Comparing matrices {i} and {j}", currentComparison, totalComparisons);
                    
                    if (AreMatricesEqual(level.FillMatrixes[i], level.FillMatrixes[j]))
                    {
                        result.IsValid = false;
                        result.Errors.Add(new ValidationError(
                            "DuplicateMatrix", 
                            $"Matrix {i} is identical to matrix {j}",
                            i,
                            $"Duplicate found at index {j}"
                        ));
                    }
                    
                    await DelayForUI();
                }
            }
            
            ReportProgress(progress, "Matrix uniqueness validation completed", totalComparisons, totalComparisons);
        }
        
        private bool AreMatricesEqual(FillMatrix a, FillMatrix b)
        {
            if (a.RowCount != b.RowCount || a.ColCount != b.ColCount)
                return false;

            for (int row = 0; row < a.RowCount; row++)
            {
                for (int col = 0; col < a.ColCount; col++)
                {
                    char letterA = a[row, col].GetCharLetter();
                    char letterB = b[row, col].GetCharLetter();
                    if (letterA != letterB)
                        return false;
                }
            }

            return true;
        }
        
        protected override string CreateSummary(ValidationResult result)
        {
            if (result.IsValid)
            {
                return $"{ServiceName}: All matrices are unique";
            }
            else
            {
                int duplicateCount = result.Errors.Count;
                return $"{ServiceName}: Found {duplicateCount} duplicate matrix pair(s)";
            }
        }
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using Random = UnityEngine.Random;
using VInspector;

public struct Directions
{
    public static Vector2Int[] directions = new Vector2Int[]
    {
        new Vector2Int(0, 1),   // Right
        new Vector2Int(1, 0),   // Down
        new Vector2Int(0, -1),  // Left
        new Vector2Int(-1, 0)   // Up
    };

    public static Vector2Int Right = new Vector2Int(0, 1);
    public static Vector2Int Down = new Vector2Int(1, 0);
    public static Vector2Int Left = new Vector2Int(0, -1);
    public static Vector2Int Up = new Vector2Int(-1, 0);
}
public class LetterInfo
{
    public string Word;
    public int IndexInWord;

    public LetterInfo(string word, int indexInWord)
    {
        Word = word;
        IndexInWord = indexInWord;
    }
}
public class LevelsPregenerator : MonoBehaviour
{
    [SerializeField] private WordDistributionSO wordDistributionSO;
    [SerializeField] private LevelsData levelsData;
    [SerializeField] private WordsData wordsData;
    [SerializeField] private LevelsConfigSO levelsConfig;
    [SerializeField] private BannedData badWords;
    [SerializeField] private int generateCount = 3;
    [SerializeField] private int levelNumToGenerate = 1;
    [SerializeField] private int maxAttemptsPerMatrix = 100;
    [SerializeField] private int fromWhatLevelRepeat = 40;

    private int fromStart = 38;
    private int toEnd = 49;
    private LetterInfo[,] letterInfos;

    private Dictionary<int, List<string>> wordsDictionary = new();
    public Dictionary<int, List<string>> WordsDictionary { get => wordsDictionary; set => wordsDictionary = value; }

    List<Vector2Int> emptyCells = new List<Vector2Int>();
    private char[,] currentMatrix;
    private Stack<Vector2Int> currentCells = new Stack<Vector2Int>();

    #region Word Distribution

    [Button("Create Word Distribution")]
    public void CreateWordDistribution()
    {
        wordsDictionary = new();
        foreach (WordsSet set in wordsData.WordsSets)
            wordsDictionary.Add(set.LettersCount, set.WordsArray.ToList());

        wordDistributionSO.levelWordsList.Clear();

        foreach (Level level in levelsData.Levels)
        {
            List<string> levelWords = new();
            int wordsCount = level.RowsCount * level.ColumnsCount / level.LettersCount;

            for (int i = 0; i < wordsCount; i++)
            {
                if (wordsDictionary[level.LettersCount].Count > 0)
                {
                    levelWords.Add(wordsDictionary[level.LettersCount][0]);
                    wordsDictionary[level.LettersCount].RemoveAt(0);
                }
            }

            wordDistributionSO.levelWordsList.Add(new WordDistributionSO.LevelWords
            {
                levelIndex = level.LevelNum,
                words = levelWords
            });
        }
        int levelNum = levelsData.Levels[^1].LevelNum;
        bool enoughWords = true;
        while (enoughWords)
        {
            levelNum++;
            int refNum = GetReferenceIndex(levelNum, fromStart, toEnd);
            //Debug.Log($"Level {levelNum} copy {refNum} with level {levelsData.Levels[refNum].LevelNum}");
            Level levelRef = levelsData.Levels[refNum];
            int lettersCount = levelRef.LettersCount;
            int wordsCountNew = levelRef.RowsCount * levelRef.ColumnsCount / lettersCount;
            if (wordsDictionary[lettersCount].Count < wordsCountNew)
            {
                Debug.Log($"Not enough words with {lettersCount} letters!");
                enoughWords = false;
                break;
            }
            List<string> levelWords = new();
            for (int i = 0; i < wordsCountNew; i++)
            {
                levelWords.Add(wordsDictionary[lettersCount][0]);
                wordsDictionary[lettersCount].RemoveAt(0);

            }
            wordDistributionSO.levelWordsList.Add(new WordDistributionSO.LevelWords
            {
                levelIndex = levelNum,
                words = levelWords
            });
        }



#if UNITY_EDITOR
        EditorUtility.SetDirty(wordDistributionSO);
        AssetDatabase.SaveAssets();
#endif

        Debug.Log("Word distribution created and saved in ScriptableObject.");
    }


    [Button("Generate all levels")]
    public void GenerateLevels()
    {
        ClearLevelsFolder();
        foreach (Level level in levelsData.Levels)
        {
            GenerateAndSaveLevel(level.LevelNum, level);
        }

        int start = fromStart;
        int end = toEnd;
        int index = start;

        for (int i = levelsData.Levels.Count; i < wordDistributionSO.levelWordsList.Count; i++)
        {
            Level refLevel = levelsData.Levels[index];
            int levelNum = i + 2;

            GenerateAndSaveLevel(levelNum, refLevel);
            index = ++index > end ? start : index;
        }

#if UNITY_EDITOR
        LoadLevelsFromFolder();
        AssetDatabase.SaveAssets();
#endif
    }
    int GetReferenceIndex(int currentLevel, int fromStart, int toEnd)
    {
        int lastLevelNum = levelsData.Levels[^1].LevelNum;
        int length = toEnd - fromStart + 1;
        return fromStart + ((currentLevel - lastLevelNum - 1) % length);
    }


    [Button("Generate one level")]
    public void GenerateOneLevel()
    {
        if (levelNumToGenerate >= levelsData.Levels.Count)
        {
            Level refLevel = levelsData.Levels[GetReferenceIndex(levelNumToGenerate, fromStart, toEnd)];
            GenerateAndSaveLevel(levelNumToGenerate, refLevel);
        }
        else
        {
            Level refLevel = levelsData.Levels[levelNumToGenerate - 2];
            GenerateAndSaveLevel(levelNumToGenerate, refLevel);
        }

#if UNITY_EDITOR
        LoadLevelsFromFolder();
        AssetDatabase.SaveAssets();
#endif
    }


    [Button("Load Levels From Folder")]
    private void LoadLevelsFromFolder()
    {
#if UNITY_EDITOR
        levelsConfig.Levels.Clear();
        string folderPath = "Assets/_Project/Prefabs/SO/Levels";

        string[] guids = AssetDatabase.FindAssets("t:LevelsSO", new[] { folderPath });
        List<LevelsSO> foundLevels = guids
            .Select(guid => AssetDatabase.LoadAssetAtPath<LevelsSO>(AssetDatabase.GUIDToAssetPath(guid)))
            .Where(level => level != null)
            .ToList();

        levelsConfig.Levels = foundLevels;

        int count = levelsConfig.Levels.Count - (fromWhatLevelRepeat - 2);
        levelsConfig.LastLevels = levelsConfig.Levels.TakeLast(count).ToList();
        //Shuffle(levelsConfig.LastLevels);
        Debug.Log($"Loaded {levelsConfig.Levels.Count} levels from folder {folderPath}");

        EditorUtility.SetDirty(this);
        AssetDatabase.SaveAssets();
#endif
    }
    #endregion

    private void GenerateAndSaveLevel(int levelNum, Level baseLevel)
    {
        LevelsSO levelSO = ScriptableObject.CreateInstance<LevelsSO>();
        levelSO.FillFromLevel(baseLevel);
        levelSO.LevelNum = levelNum;

        var levelWordsData = wordDistributionSO.levelWordsList.Find(lw => lw.levelIndex == levelNum);
        levelSO.Words = levelWordsData.words;

        GenerateLevelNTimes(levelSO, generateCount);

#if UNITY_EDITOR
        string folderPath = "Assets/_Project/Prefabs/SO/Levels";
        AssetDatabase.CreateFolder("Assets/_Project/Prefabs/SO/", "Levels");
        string assetPath = $"{folderPath}/Level{levelSO.LevelNum}.asset";
        AssetDatabase.CreateAsset(levelSO, assetPath);
#endif
    }
    private void ClearLevelsFolder()
    {
#if UNITY_EDITOR
        string folderPath = "Assets/_Project/Prefabs/SO/Levels";
        if (!AssetDatabase.IsValidFolder(folderPath))
            return;

        string[] assetGUIDs = AssetDatabase.FindAssets("", new[] { folderPath });

        foreach (string guid in assetGUIDs)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            if (path.EndsWith(".asset")) 
            {
                AssetDatabase.DeleteAsset(path);
            }
        }

        AssetDatabase.Refresh();
#endif
    }

    #region Matrix Generation

    private void GenerateLevelNTimes(LevelsSO level, int n)
    {
        level.FillMatrixes = new List<FillMatrix>();
        for (int i = 0; i < n; i++)
        {
            bool success = false;
            for (int attempt = 0; attempt < maxAttemptsPerMatrix; attempt++)
            {
                currentMatrix = GenerateNewMatrixForLevel(level, out bool isFull);

                if (!isFull)
                {
                    continue;
                }
                if (!IsMatrixValid(currentMatrix, level, out string failReason, out string failWord))
                {
                    continue;
                }

                FillMatrix fillMatrix = new FillMatrix(level.RowsCount, level.ColumnsCount);

                for (int r = 0; r < level.RowsCount; r++)
                {
                    for (int c = 0; c < level.ColumnsCount; c++)
                    {
                        fillMatrix[r, c] = new LetterData(
                            currentMatrix[r, c].ToString(),
                            letterInfos[r, c]?.Word ?? "",
                            letterInfos[r, c]?.IndexInWord ?? -1
                        );
                    }
                }
                level.FillMatrixes.Add(fillMatrix);

                success = true;
                break;
            }
            if (!success)
            {
                Debug.LogError($"Failed to generate matrix {i} for level {level.LevelNum} after {maxAttemptsPerMatrix} attempts. Generate one level manually.");
            }
        }
    }

    private char[,] GenerateNewMatrixForLevel(LevelsSO level, out bool allWordsPlaced)
    {
        letterInfos = new LetterInfo[level.RowsCount, level.ColumnsCount];
        currentMatrix = new char[level.RowsCount, level.ColumnsCount];
        emptyCells.Clear();
        currentCells.Clear();
        FillMatrixWithEmptyCells();

        allWordsPlaced = true;
        foreach (string word in level.Words)
        {
            bool isReversed = RollChance(level.BackwardsProbability);
            if (!TryAddWord(word, isReversed))
            {
                allWordsPlaced = false;
                break;
            }
        }

        return currentMatrix;
    }

    public static bool RollChance(int chancePercent)
    {
        chancePercent = Mathf.Clamp(chancePercent, 0, 100);
        return Random.Range(0, 100) < chancePercent;
    }

    private void FillMatrixWithEmptyCells()
    {
        for (int i = 0; i < currentMatrix.GetLength(0); i++)
        {
            for (int j = 0; j < currentMatrix.GetLength(1); j++)
            {
                currentMatrix[i, j] = '0';
                emptyCells.Add(new Vector2Int(i, j));
            }
        }
    }

    private bool TryAddWord(string word, bool isReversed)
    {
        Shuffle(emptyCells);
        foreach (Vector2Int cell in new List<Vector2Int>(emptyCells))
        {
            AddLetterToCell(word[0], cell, word, 0);
            if (TryAddWordRecursive(cell, word, 1, isReversed))
                return true;

            ClearLastCell();
        }

        return false;
    }

    private bool TryAddWordRecursive(Vector2Int currentPos, string word, int index, bool isReversed)
    {
        if (index == word.Length)
            return true;

        Shuffle(Directions.directions);
        foreach (Vector2Int dir in Directions.directions)
        {
            Vector2Int newPos = currentPos + dir;
            if (isReversed && index == 1 && (dir == Directions.Right || dir == Directions.Down))
            {
                continue;
            }
            if (!isReversed && (dir == Directions.Left || dir == Directions.Up))
            {
                continue;
            }
            if (IsCellInside(newPos) && IsCellEmpty(newPos))
            {
                AddLetterToCell(word[index], newPos, word, index);
                if (TryAddWordRecursive(newPos, word, index + 1, isReversed))
                    return true;

                ClearLastCell();
            }
        }

        return false;
    }

    private void Shuffle<T>(IList<T> list)
    {
        for (int i = list.Count - 1; i > 0; i--)
        {
            int j = Random.Range(0, i + 1);
            (list[i], list[j]) = (list[j], list[i]);
        }
    }

    private void Shuffle(Vector2Int[] array)
    {
        for (int i = array.Length - 1; i > 0; i--)
        {
            int j = Random.Range(0, i + 1);
            (array[i], array[j]) = (array[j], array[i]);
        }
    }

    private void AddLetterToCell(char letter, Vector2Int cell, string word, int indexInWord)
    {
        currentMatrix[cell.x, cell.y] = letter;
        letterInfos[cell.x, cell.y] = new LetterInfo(word, indexInWord);
        currentCells.Push(cell);
        emptyCells.Remove(cell);
    }

    private void ClearLastCell()
    {
        Vector2Int cell = currentCells.Pop();
        currentMatrix[cell.x, cell.y] = '0';
        emptyCells.Add(cell);
    }
    #endregion

    #region Matrix Validation
    private bool IsMatrixValid(char[,] matrix, LevelsSO level, out string failReason, out string failWord)
    {
        failReason = "";
        failWord = "";

        if (EqualToMatrixes(matrix, level.FillMatrixes))
        {
            failReason = "Matrix is identical to existing matrix";
            return false;
        }

        if (!LevelsPregeneratorExtension.ValidateMatrixForBadWords(matrix, badWords.Words))
        {
            failReason = "Matrix contains bad words";
            return false;
        }

        foreach (string word in level.Words)
        {
            if (HasAmbiguousPath(matrix, word))
            {
                failReason = $"Word has multiple paths";
                failWord = word;
                return false;
            }
        }

        return true;
    }

    private bool EqualToMatrixes(char[,] matrix, List<FillMatrix> other)
    {
        foreach (FillMatrix otherMatrix in other)
        {
            if (AreMatricesEqual(matrix, otherMatrix))
            {
                return true;
            }
        }
        return false;
    }
    private bool AreMatricesEqual(char[,] a, FillMatrix b)
    {
        if (a.GetLength(0) != b.RowCount || a.GetLength(1) != b.ColCount) return false;

        for (int row = 0; row < a.GetLength(0); row++)
            for (int col = 0; col < a.GetLength(1); col++)
            {
                char la = a[row, col];
                char lb = b[row, col].GetCharLetter();
                if (la != lb) return false;
            }

        return true;
    }
    private bool IsPalindrome(string word)
    {
        int len = word.Length;
        for (int i = 0; i < len / 2; i++)
        {
            if (word[i] != word[len - 1 - i])
                return false;
        }
        return true;
    }
    private bool HasAmbiguousPath(char[,] matrix, string word)
    {
        int rows = matrix.GetLength(0);
        int cols = matrix.GetLength(1);
        int totalPaths = 0;

        for (int r = 0; r < rows; r++)
        {
            for (int c = 0; c < cols; c++)
            {
                if (matrix[r, c] != word[0])
                    continue;

                bool[,] visited = new bool[rows, cols];
                List<Vector2Int> path = new List<Vector2Int> { new Vector2Int(r, c) };

                CountPaths(matrix, word, r, c, 0, visited, ref totalPaths, path);
                bool isPalindrome = IsPalindrome(word);
                if (totalPaths >= 2 && !isPalindrome || totalPaths >= 3 && isPalindrome)
                {
                    return true;
                }
            }
        }

        return false;
    }
    private void CountPaths(char[,] matrix, string word, int r, int c, int index, bool[,] visited, ref int totalPaths, List<Vector2Int> path)
    {
        int rows = matrix.GetLength(0);
        int cols = matrix.GetLength(1);

        if (matrix[r, c] != word[index])
            return;

        if (index == word.Length - 1)
        {
            totalPaths++;
            return;
        }

        visited[r, c] = true;

        foreach (var dir in Directions.directions)
        {
            int nr = r + dir.x;
            int nc = c + dir.y;

            if (nr < 0 || nr >= rows || nc < 0 || nc >= cols || visited[nr, nc])
                continue;

            if (matrix[nr, nc] == word[index + 1])
            {
                path.Add(new Vector2Int(nr, nc));
                CountPaths(matrix, word, nr, nc, index + 1, visited, ref totalPaths, path);
                path.RemoveAt(path.Count - 1);

                if (totalPaths >= 2)
                {
                    visited[r, c] = false;
                    return;
                }
            }
        }

        visited[r, c] = false;
    }



    #endregion

    #region Helper Methods
    public bool IsCellEmpty(int r, int c)
    {
        return currentMatrix[r, c] == '0';
    }
    private bool IsCellEmpty(Vector2Int pos)
    {
        return IsCellEmpty(pos.x, pos.y);
    }
    private bool IsCellInside(int r, int c)
    {
        int rows = currentMatrix.GetLength(0);
        int cols = currentMatrix.GetLength(1);
        return r >= 0 && r < rows && c >= 0 && c < cols;
    }
    private bool IsCellInside(Vector2Int pos)
    {
        return IsCellInside(pos.x, pos.y);
    }
    #endregion

}


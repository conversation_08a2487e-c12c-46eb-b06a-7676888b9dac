using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using Cysharp.Threading.Tasks;
using LevelEditor.Services;

namespace LevelEditor
{
    /// <summary>
    /// Тестер для проверки работы YandexDictionaryService с фильтрацией
    /// </summary>
    public class YandexDictionaryTester : EditorWindow
    {
        private string _apiKey = "";
        private string _testWord = "";
        private YandexDictionaryService _service;
        private Vector2 _scrollPosition;
        private List<string> _testResults = new List<string>();
        private bool _isTesting = false;
        private YandexFilteringConfig _config;

        [MenuItem("Tools/Level Editor/Yandex Dictionary Tester")]
        public static void ShowWindow()
        {
            var window = GetWindow<YandexDictionaryTester>("Yandex Dictionary Tester");
            window.maxSize = new Vector2(600, 800);
            window.minSize = new Vector2(400, 300);
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("Yandex Dictionary Service Tester", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // Configuration file
            EditorGUILayout.LabelField("Configuration:");
            _config = (YandexFilteringConfig)EditorGUILayout.ObjectField("Config File", _config, typeof(YandexFilteringConfig), false);
            EditorGUILayout.Space();

            // API Key input (fallback if no config)
            if (_config == null)
            {
                EditorGUILayout.LabelField("API Key (manual):");
                _apiKey = EditorGUILayout.PasswordField(_apiKey);
                EditorGUILayout.Space();
            }
            else
            {
                EditorGUILayout.LabelField($"Using API key from config: {(!string.IsNullOrEmpty(_config.YandexApiKey) ? "✅ Set" : "❌ Not set")}");
                EditorGUILayout.Space();
            }

            // Test word input
            EditorGUILayout.LabelField("Test Word:");
            _testWord = EditorGUILayout.TextField(_testWord);
            EditorGUILayout.Space();

            // Buttons
            EditorGUILayout.BeginHorizontal();

            bool hasApiKey = (_config != null && !string.IsNullOrEmpty(_config.YandexApiKey)) ||
                           (_config == null && !string.IsNullOrEmpty(_apiKey));

            GUI.enabled = hasApiKey && !string.IsNullOrEmpty(_testWord) && !_isTesting;
            if (GUILayout.Button("Test Single Word"))
            {
                TestSingleWord();
            }

            if (GUILayout.Button("Test Multiple Words"))
            {
                TestMultipleWords();
            }

            if (GUILayout.Button("Test Problem Words"))
            {
                TestProblemWords();
            }
            GUI.enabled = true;

            if (GUILayout.Button("Clear Results"))
            {
                _testResults.Clear();
            }

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.Space();

            // Status
            if (_isTesting)
            {
                EditorGUILayout.LabelField("Testing...", EditorStyles.helpBox);
            }

            // Results
            EditorGUILayout.LabelField("Results:", EditorStyles.boldLabel);
            _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);
            
            foreach (string result in _testResults)
            {
                EditorGUILayout.LabelField(result, EditorStyles.wordWrappedLabel);
            }
            
            EditorGUILayout.EndScrollView();
        }

        private async void TestSingleWord()
        {
            _isTesting = true;
            _testResults.Add($"=== Testing word: '{_testWord}' ===");

            try
            {
                if (_config != null)
                {
                    _service = _config.CreateYandexService();
                    _testResults.Add("Using configuration from ScriptableObject");
                }
                else
                {
                    _service = new YandexDictionaryService(_apiKey, 10);
                    _testResults.Add("Using manual API key");
                }

                if (_service == null)
                {
                    _testResults.Add("❌ Failed to create service - check API key");
                    return;
                }

                bool isValid = await _service.IsValidWordAsync(_testWord);

                _testResults.Add($"Result: {(isValid ? "✅ VALID" : "❌ INVALID")}");
            }
            catch (System.Exception ex)
            {
                _testResults.Add($"❌ Error: {ex.Message}");
            }
            finally
            {
                _isTesting = false;
                Repaint();
            }
        }

        private async void TestMultipleWords()
        {
            _isTesting = true;
            
            // Тестовые слова: смесь обычных слов, междометий и редких слов
            var testWords = new List<string>
            {
                "дом",          // обычное слово - должно пройти
                "кот",          // обычное слово - должно пройти
                "ах",           // междометие - должно быть отклонено
                "ой",           // междометие - должно быть отклонено
                "брр",          // междометие - должно быть отклонено
                "стол",         // обычное слово - должно пройти
                "книга",        // обычное слово - должно пройти
                "эх",           // междометие - должно быть отклонено
                "ура",          // междометие - должно быть отклонено
                "солнце",       // обычное слово - должно пройти
                "hello",        // английское слово - должно быть отклонено
                "123",          // цифры - должно быть отклонено
                "а",            // слишком короткое - должно быть отклонено
                "очень-длинное-слово", // слишком длинное - должно быть отклонено
            };

            _testResults.Add("=== Testing multiple words ===");
            
            try
            {
                if (_config != null)
                {
                    _service = _config.CreateYandexService();
                    _testResults.Add("Using configuration from ScriptableObject");
                }
                else
                {
                    _service = new YandexDictionaryService(_apiKey, 10);
                    _testResults.Add("Using manual API key");
                }

                if (_service == null)
                {
                    _testResults.Add("❌ Failed to create service - check API key");
                    return;
                }
                
                foreach (string word in testWords)
                {
                    _testResults.Add($"Testing: '{word}'...");
                    bool isValid = await _service.IsValidWordAsync(word);
                    _testResults.Add($"  Result: {(isValid ? "✅ VALID" : "❌ INVALID")}");
                    
                    // Небольшая задержка между запросами
                    await UniTask.Delay(500);
                    Repaint();
                }
                
                _testResults.Add("=== Testing completed ===");
            }
            catch (System.Exception ex)
            {
                _testResults.Add($"❌ Error: {ex.Message}");
            }
            finally
            {
                _isTesting = false;
                Repaint();
            }
        }

        private async void TestProblemWords()
        {
            _isTesting = true;

            // Тестируем слова, которые должны быть валидными, но отклоняются
            var problemWords = new List<string>
            {
                "ЦВЕТ",         // цвет - обычное существительное
                "СИЛА",         // сила - обычное существительное
                "ВЕРА",         // вера - обычное существительное
                "УДАР",         // удар - обычное существительное
                "ДОМ",          // дом - обычное существительное
                "КОТ"           // кот - обычное существительное
            };

            _testResults.Add("=== Testing problem words (should be valid) ===");

            try
            {
                if (_config != null)
                {
                    _service = _config.CreateYandexService();
                    _testResults.Add("Using configuration from ScriptableObject");
                }
                else
                {
                    _service = new YandexDictionaryService(_apiKey, 10);
                    _testResults.Add("Using manual API key");
                }

                if (_service == null)
                {
                    _testResults.Add("❌ Failed to create service - check API key");
                    return;
                }

                foreach (string word in problemWords)
                {
                    _testResults.Add($"Testing: '{word}'...");
                    bool isValid = await _service.IsValidWordAsync(word);
                    _testResults.Add($"  Result: {(isValid ? "✅ VALID" : "❌ INVALID")}");

                    // Небольшая задержка между запросами
                    await UniTask.Delay(1000);
                    Repaint();
                }

                _testResults.Add("=== Problem words testing completed ===");
            }
            catch (System.Exception ex)
            {
                _testResults.Add($"❌ Error: {ex.Message}");
            }
            finally
            {
                _isTesting = false;
                Repaint();
            }
        }
    }
}

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using VG;
using TMPro;
using System;
using System.Linq;

public class AnswersView : MonoBehaviour
{
    [Header("Answers")]
    [SerializeField] private HorizontalLayoutGroup answerPrefab;
    [SerializeField] private HorizontalLayoutGroup horizontalLinePrefab;
    [SerializeField] private Transform verticalLine;
    [SerializeField] int maxAnswerLength = 18;
    [SerializeField] private RectTransform pool;

    [SerializeField] private LetterDotsConfig _letterDotsConfig;

    [SerializeField] private GridLayoutGroup wordsGrid;
    // ��� ������ (�����)
    private List<HorizontalLayoutGroup> lines = new();

    // ��� ������ ���� (�� �����)
    private List<HorizontalLayoutGroup> wordGroups = new();

    bool isChanging = false;

    private void Awake()
    {
        Resolutions.onResolutionChanged += UpdateAnswers;
    }

    private void OnDestroy()
    {
        Resolutions.onResolutionChanged -= UpdateAnswers;
    }
    public void ClearAnswers()
    {
        foreach (var line in lines)
        {
            Debug.Log("Destroy! " + line.name, line);
            Destroy(line.gameObject);
        }
        lines.Clear();
        wordGroups.Clear();
    }
    public void UpdateAnswers(int width, int height)
    {
        if (LevelManager.CurrentWords.Count == 0 || isChanging) return;
        isChanging = true;

        VerticalLayoutGroup verticalLayoutGroup = verticalLine.GetComponent<VerticalLayoutGroup>();
        verticalLayoutGroup.enabled = true;
        maxAnswerLength = width < height ? _letterDotsConfig.DotsInRow[wordsGrid.constraintCount] : 12;

        // ������������ ��� ������ � ������
        foreach (var line in lines)
        {
            line.gameObject.SetActive(false);
            foreach (Transform group in line.transform)
                group.gameObject.SetActive(false);
        }

        int currLength = 0;
        int lineIndex = 0;
        int groupIndex = 0;

        HorizontalLayoutGroup currentLine = GetOrCreateLine(lineIndex);

        foreach (string word in LevelManager.Instance.WordsDot.Keys)
        {
            List<RectTransform> dots = LevelManager.Instance.WordsDot[word];

            HorizontalLayoutGroup wordGroup = GetOrCreateWordGroup(groupIndex++);
            wordGroup.transform.SetParent(currentLine.transform, false);
            wordGroup.gameObject.SetActive(true);

            foreach (RectTransform dot in dots)
            {
                dot.SetParent(wordGroup.transform, false);
                dot.gameObject.SetActive(true);
                currLength++;
                //Debug.Log($"Dot! currLength {currLength}", dot);
            }
            if (currLength > maxAnswerLength)
            {
                //Debug.Log($"New Line! {currLength} max {maxAnswerLength}");
                lineIndex++;
                currentLine = GetOrCreateLine(lineIndex);
                currLength = 0;
            }
        }
        isChanging = false;
    }

    private HorizontalLayoutGroup GetOrCreateLine(int index)
    {
        if (index < lines.Count)
        {
            lines[index].gameObject.SetActive(true);
            lines[index].transform.SetAsLastSibling();
            return lines[index];
        }

        var line = Instantiate(horizontalLinePrefab, verticalLine);
        line.transform.SetAsLastSibling();
        lines.Add(line);
        return line;
    }

    private HorizontalLayoutGroup GetOrCreateWordGroup(int index)
    {
        if (index < wordGroups.Count)
        {
            return wordGroups[index];
        }

        var group = Instantiate(answerPrefab);
        wordGroups.Add(group);
        return group;
    }


}

using System.Collections;
using System;
using System.Collections.Generic;
using UnityEngine;


[Serializable]
[CreateAssetMenu(menuName = "Scriptable Objects/Levels", fileName = "Level")]
public class LevelsSO : ScriptableObject
{
    [SerializeField] private int levelNum;
    [SerializeField] private int lettersCount;
    [SerializeField] private int rowsCount;
    [SerializeField] private int columnsCount;
    [SerializeField] private int backwardsProbability;
 
    [SerializeField] private List<FillMatrix> fillMatrixes;
    [SerializeField] private List<string> words;
    [SerializeField] private List<BonusWordData> bonusWords = new List<BonusWordData>();
    public int LevelNum
    {
        get { return levelNum; }
        set { levelNum = value; }
    }

    public int LettersCount
    {
        get { return lettersCount; }
        set { lettersCount = value; }
    }

    public int RowsCount
    {
        get { return rowsCount; }
        set { rowsCount = value; }
    }

    public int ColumnsCount
    {
        get { return columnsCount; }
        set { columnsCount = value; }
    }

    //public char[] FillMatrix { get => fillMatrix; set => fillMatrix = value; }
    public List<string> Words { get => words; set => words = value; }
    //public char[] FillMatrixes { get => fillMatrixes; set => fillMatrixes = value; }
    public List<FillMatrix> FillMatrixes { get => fillMatrixes; set => fillMatrixes = value; }
    public List<BonusWordData> BonusWords { get => bonusWords; set => bonusWords = value; }
    public int BackwardsProbability { get => backwardsProbability; set => backwardsProbability = value; }

    public void FillFromLevel(Level level)
    {
        this.levelNum = level.LevelNum;
        this.lettersCount = level.LettersCount;
        this.rowsCount = level.RowsCount;
        this.columnsCount = level.ColumnsCount;
        this.BackwardsProbability = level.BackwardsProbability;
    }
}

// ������� ��������� �������, ����� ����� ���� �������������
[Serializable]
public class FillMatrix
{
    [SerializeField] private List<Row> rows;
    // ���������� �����
    public int RowCount => rows.Count;
    // ���������� ��������
    public int ColCount => rows.Count > 0 ? rows[0].Letters.Count : 0;

    public FillMatrix(int rowCount, int colCount)
    {
        rows = new List<Row>();
        for (int i = 0; i < rowCount; i++)
        {
            rows.Add(new Row(colCount));
        }
    }

    public LetterData this[int row, int col]
    {
        get => rows[row][col];
        set => rows[row][col] = value;
    }

    public List<Row> Rows => rows;
}
[Serializable]
public class Row
{
    [SerializeField] private List<LetterData> row;

    public List<LetterData> Letters => row;

    public Row(int size)
    {
        row = new List<LetterData>();
        for (int i = 0; i < size; i++)
        {
            row.Add(new LetterData(" ", "", -1));
        }
    }

    public LetterData this[int i]
    {
        get => row[i];
        set => row[i] = value;
    }
}
[Serializable]
public class LetterData
{
    // ����� - string, ����� ���������� � ���������� �������
    public string Letter;
    // �������� �� �����
    public string Word;
    // ������ � �����
    public int IndexInWord;

    public LetterData(string letter, string word, int index)
    {
        Letter = letter;
        Word = word;
        IndexInWord = index;
    }
    public char GetCharLetter()
    {
        return Convert.ToChar(Letter);
    }
}

[System.Serializable]
public class BonusWordData
{
    public string Word;
    public int MatrixIndex;
    public int Length;

    public BonusWordData(string word, int matrixIndex)
    {
        Word = word;
        MatrixIndex = matrixIndex;
        Length = word.Length;
    }
}
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

public class Tools
{
    [MenuItem("Tools/" + nameof(PlayFromStartupScene))]
    private static void PlayFromStartupScene()
    {
        Canvas[] canvases = Object.FindObjectsOfType<Canvas>(true);

        foreach (Canvas canvas in canvases)
        {
            if (canvas.gameObject.name == "Finger")
                continue;

            canvas.gameObject.SetActive(true);
        }

        EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());

        if (EditorSceneManager.SaveCurrentModifiedScenesIfUserWantsTo() == false)
            return;

        if (EditorApplication.isPlaying == true)
            return;

        string scenePath = SceneUtility.GetScenePathByBuildIndex(0);
        EditorSceneManager.OpenScene(scenePath);
        EditorApplication.isPlaying = true;
    }
}

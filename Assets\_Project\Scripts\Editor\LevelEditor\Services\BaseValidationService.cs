using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace LevelEditor.Services
{
    public abstract class BaseValidationService : IValidationService
    {
        public abstract string ServiceName { get; }
        
        public async UniTask<ValidationResult> ValidateAsync(LevelsSO level, IProgress<ValidationProgress> progress = null)
        {
            var result = new ValidationResult(ServiceName);
            
            try
            {
                await ValidateInternalAsync(level, result, progress);
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add(new ValidationError("Exception", $"Validation failed with exception: {ex.Message}"));
                Debug.LogError($"[{ServiceName}] Validation failed: {ex}");
            }
            
            result.Summary = CreateSummary(result);
            return result;
        }
        
        protected abstract UniTask ValidateInternalAsync(LevelsSO level, ValidationResult result, IProgress<ValidationProgress> progress);
        
        protected virtual string CreateSummary(ValidationResult result)
        {
            if (result.IsValid)
            {
                return $"{ServiceName}: Validation passed";
            }
            else
            {
                return $"{ServiceName}: Found {result.Errors.Count} error(s)";
            }
        }
        
        protected void ReportProgress(IProgress<ValidationProgress> progress, string operation, int processed, int total)
        {
            progress?.Report(new ValidationProgress(ServiceName, operation, processed, total));
        }
        
        protected async UniTask DelayForUI()
        {
            // Small delay to allow UI updates
            await UniTask.Delay(1, DelayType.DeltaTime);
        }
    }
}

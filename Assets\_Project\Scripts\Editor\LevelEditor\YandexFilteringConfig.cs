using UnityEngine;
using LevelEditor.Services;

namespace LevelEditor
{
    /// <summary>
    /// Конфигурация фильтрации для Yandex Dictionary Service
    /// </summary>
    [CreateAssetMenu(fileName = "YandexFilteringConfig", menuName = "Level Editor/Yandex Filtering Config")]
    public class YandexFilteringConfig : ScriptableObject
    {
        [Header("API Settings")]
        [SerializeField] private string _yandexApiKey = "";
        [SerializeField] private int _requestTimeoutSeconds = 10;
        
        [Header("Basic Filtering")]
        [SerializeField] private bool _filterInterjections = true;
        [SerializeField] private bool _filterParticles = true;
        [SerializeField] private bool _filterAbbreviations = true;
        
        [Header("Quality Filtering")]
        [SerializeField] private bool _filterObsoleteWords = true;
        [SerializeField] private bool _filterSlangWords = true;
        [SerializeField] private bool _filterVulgarWords = true;
        [SerializeField] private bool _filterTechnicalTerms = true;
        [SerializeField] private bool _filterDialectalWords = true;
        
        [Header("Word Requirements")]
        [SerializeField] private bool _requireRussianLettersOnly = true;
        [SerializeField] private int _minBonusWordLength = 3;
        [SerializeField] private int _maxBonusWordLength = 12;
        
        public string YandexApiKey => _yandexApiKey;
        public int RequestTimeoutSeconds => _requestTimeoutSeconds;
        
        /// <summary>
        /// Создает ValidationServiceSettings на основе этой конфигурации
        /// </summary>
        public ValidationServiceSettings CreateValidationSettings()
        {
            return new ValidationServiceSettings
            {
                UseYandexDictionary = !string.IsNullOrEmpty(_yandexApiKey),
                YandexApiKey = _yandexApiKey,
                RequestTimeoutSeconds = _requestTimeoutSeconds,
                
                FilterInterjections = _filterInterjections,
                FilterParticles = _filterParticles,
                FilterAbbreviations = _filterAbbreviations,
                FilterObsoleteWords = _filterObsoleteWords,
                FilterSlangWords = _filterSlangWords,
                FilterVulgarWords = _filterVulgarWords,
                FilterTechnicalTerms = _filterTechnicalTerms,
                FilterDialectalWords = _filterDialectalWords,
                RequireRussianLettersOnly = _requireRussianLettersOnly,
                MinBonusWordLength = _minBonusWordLength,
                MaxBonusWordLength = _maxBonusWordLength
            };
        }
        
        /// <summary>
        /// Создает YandexDictionaryService с настройками из этой конфигурации
        /// </summary>
        public YandexDictionaryService CreateYandexService()
        {
            if (string.IsNullOrEmpty(_yandexApiKey))
            {
                Debug.LogWarning("Yandex API key is not set in YandexFilteringConfig");
                return null;
            }
            
            var settings = CreateValidationSettings();
            return new YandexDictionaryService(_yandexApiKey, _requestTimeoutSeconds, settings);
        }
        
        [ContextMenu("Test Configuration")]
        private void TestConfiguration()
        {
            Debug.Log("=== Yandex Filtering Configuration ===");
            Debug.Log($"API Key set: {!string.IsNullOrEmpty(_yandexApiKey)}");
            Debug.Log($"Timeout: {_requestTimeoutSeconds}s");
            Debug.Log($"Filter Interjections: {_filterInterjections}");
            Debug.Log($"Filter Particles: {_filterParticles}");
            Debug.Log($"Filter Abbreviations: {_filterAbbreviations}");
            Debug.Log($"Filter Obsolete: {_filterObsoleteWords}");
            Debug.Log($"Filter Slang: {_filterSlangWords}");
            Debug.Log($"Filter Vulgar: {_filterVulgarWords}");
            Debug.Log($"Filter Technical: {_filterTechnicalTerms}");
            Debug.Log($"Filter Dialectal: {_filterDialectalWords}");
            Debug.Log($"Russian Only: {_requireRussianLettersOnly}");
            Debug.Log($"Word Length: {_minBonusWordLength}-{_maxBonusWordLength}");
        }
    }
}

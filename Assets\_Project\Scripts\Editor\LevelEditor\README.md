# Level Editor System

Новая система редактирования уровней с асинхронной генерацией и валидацией.

## Архитектура

### Сервисы

**ILevelGenerationService** - интерфейс для сервисов генерации уровней
- `LevelGenerationService` - основной сервис генерации уровней

**IValidationService** - интерфейс для валидационных сервисов
- `BadWordValidationServiceEditor` - проверка на нецензурные слова
- `MatrixIntegrityValidationService` - проверка целостности матриц
- `MatrixUniquenessValidationService` - проверка уникальности матриц
- `SingleLineCollectionValidationService` - проверка на прямолинейное расположение слов

### Окна редактора

**LevelEditorWindow** - главное окно редактора с вкладками:
- Вкладка "Generation" - генерация уровней
- Вкладка "Validation" - валидация уровней
- Вкладка "Settings" - настройки генерации и пути к файлам
- Вкладка "Help" - справочная информация и документация

**ProgressWindow** - окно прогресса для асинхронных операций

## Использование

### Открытие редактора
```
Tools → Level Editor
```

### Генерация уровней

1. **Создание распределения слов**
   - Нажмите "Create Word Distribution"
   - Дождитесь завершения операции

2. **Генерация всех уровней**
   - Нажмите "Generate All Levels"
   - Следите за прогрессом в окне прогресса

3. **Генерация одного уровня**
   - Введите номер уровня
   - Нажмите "Generate One Level"

### Валидация уровней

1. **Выбор уровня**
   - Выберите LevelsSO в поле "Level"

2. **Настройка валидаций**
   - Отметьте нужные валидационные сервисы

3. **Запуск валидации**
   - "Validate Selected Level" - для одного уровня
   - "Validate All Levels" - для всех уровней

4. **Регенерация проблемных уровней**
   - При обнаружении ошибок появляется кнопка "Regenerate X Problematic Level(s)"
   - Автоматически регенерирует только уровни с проблемами
   - Показывает список уровней, которые будут регенерированы

### Настройки (Settings)

**Параметры генерации матриц:**
- Max Attempts Per Matrix - максимальное количество попыток генерации одной матрицы
- Matrices Per Level - количество матриц, генерируемых для каждого уровня
- Prevent Straight Line Words - предотвращение размещения слов по прямым линиям

**Условия генерации (Generation Conditions):**
- Matrix Uniqueness Check - проверка уникальности матриц в рамках уровня
- Bad Word Check - проверка на наличие нецензурных слов
- Matrix Integrity Check - проверка целостности и корректности матриц
- Straight Line Prevention - предотвращение прямолинейного размещения слов

**Настройки расширенных уровней:**
- From What Level Repeat - с какого уровня начинать повторение конфигураций
- Reference Range Start/End - диапазон уровней для использования как референс

**Пути к ресурсам:**
- Levels Folder - папка для сохранения сгенерированных уровней
- Word Distribution - путь к файлу распределения слов
- Levels Data - путь к конфигурации уровней
- Words Data - путь к словарю слов
- Levels Config - путь к конфигурации уровней игры
- Bad Words Data - путь к списку запрещенных слов

### Справка (Help)

Содержит подробную документацию по:
- Word Distribution - что это и как работает
- Generation Process - процесс генерации уровней
- Validation System - система валидации
- BackwardsProbability - настройка направления слов

## Валидационные сервисы

### Bad Words Validation
- Проверяет матрицы на наличие нецензурных слов
- Использует данные из `BannedData.asset`
- Проверяет все 8 направлений

### Matrix Integrity Validation
- Проверяет размеры матриц
- Проверяет наличие пустых ячеек
- Проверяет наличие всех слов в матрице

### Matrix Uniqueness Validation
- Проверяет уникальность матриц в рамках одного уровня
- Предотвращает дублирование матриц

### Single Line Collection Validation
- Проверяет, что слова нельзя собрать по прямой линии
- Обеспечивает наличие изгибов/поворотов в каждом слове
- Проверяет все 4 направления: вправо, вниз, влево, вверх
- Гарантирует "змейкообразное" расположение слов
- **ВАЖНО:** Проверяет только первую матрицу для оптимизации производительности

## Асинхронность

Все операции выполняются асинхронно с использованием UniTask:
- Не блокируют UI редактора
- Показывают детальный прогресс
- Позволяют отменить операцию

## Прогресс операций

Окно прогресса показывает:
- Текущую операцию
- Процент выполнения
- Лог всех операций
- Возможность отмены

## Результаты

### Генерация
- Сообщения об успехе/ошибках
- Предупреждения
- Количество сгенерированных уровней

### Валидация
- Результаты по каждому сервису
- Детальные ошибки с описанием
- Сводная информация

## Тестирование

Используйте меню `Tools → Test Level Editor` для:
- Тестирования сервиса генерации
- Тестирования валидационных сервисов
- Быстрого открытия редактора

## Миграция

Старый `BadWordCheckerEditor` удален.
Вся функциональность перенесена в новый `LevelEditorWindow`.

## Конфигурация

Пути к ресурсам настроены в сервисах:
- `LEVELS_FOLDER_PATH` - папка с уровнями
- `BANNED_DATA_PATH` - путь к запрещенным словам
- Другие пути к ScriptableObject ресурсам

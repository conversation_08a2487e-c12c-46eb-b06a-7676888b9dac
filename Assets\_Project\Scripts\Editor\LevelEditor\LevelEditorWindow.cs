using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using Cysharp.Threading.Tasks;
using LevelEditor.Services;

namespace LevelEditor
{
    public class LevelEditorWindow : EditorWindow
    {
        private enum Tab
        {
            Generation,
            Validation,
            Levels,
            WordsData,
            Settings,
            Help
        }

        private Tab currentTab = Tab.Generation;
        private Vector2 scrollPosition;

        // Services
        private ILevelGenerationService generationService;
        private List<IValidationService> validationServices;
        private BonusWordsDetectionService bonusWordsService;

        // WordsData tab fields
        private WordsData wordsData;
        private Vector2 wordsDataScrollPosition;
        private Vector2 errorsScrollPosition;
        private string wordsSearchFilter = "";
        private bool enableEncodingValidation = true;
        private bool enableCorruptedDataValidation = true;
        private List<string> validationErrors = new List<string>();
        private List<(int setIndex, int wordIndex, string word)> wordsWithErrors = new List<(int, int, string)>();
        private bool isValidating = false;

        // Generation settings
        private int levelToGenerate = 1;

        // Settings tab variables
        private int maxAttemptsPerMatrix = 5000;
        private int generateCount = 3;
        private int fromWhatLevelRepeat = 40;
        private int fromStart = 38;
        private int toEnd = 49;

        // Word validation settings
        private ValidationServiceSettings validationSettings = new ValidationServiceSettings();
        private bool showWordValidationSettings = false;
        private string levelsPath = "Assets/_Project/Prefabs/SO/Levels";
        private string wordDistributionPath = "Assets/_Project/Prefabs/SO/WordDistribution/WordDistribution.asset";
        private string levelsDataPath = "Assets/_Project/Prefabs/SO/tables/LevelsData.asset";
        private string wordsDataPath = "Assets/_Project/Prefabs/SO/tables/WordsData.asset";
        private string levelsConfigPath = "Assets/_Project/Prefabs/SO/LevelsConfig.asset";
        private string badWordsPath = "Assets/_Project/Prefabs/SO/tables/BannedData.asset";
        private bool preventStraightLines = true;
        private float bendPriority = 0.6f; // Reduced from 0.8f for faster generation
        private bool fastGeneration = true;

        // Generation Conditions Settings
        private bool enableMatrixUniquenessCheck = true;
        private bool enableBadWordCheck = true;
        private bool enableMatrixIntegrityCheck = true;
        private bool enableStraightLineCheck = true;
        private bool strictFirstMatrix = true;

        // Matrix Validation Scope Settings
        private MatrixValidationScope badWordValidationScope = MatrixValidationScope.AllMatrices;
        private MatrixValidationScope matrixIntegrityValidationScope = MatrixValidationScope.AllMatrices;
        private MatrixValidationScope matrixUniquenessValidationScope = MatrixValidationScope.AllMatrices;
        private MatrixValidationScope straightLineValidationScope = MatrixValidationScope.FirstMatrixOnly;

        // Validation settings
        private LevelsSO selectedLevel;
        private bool[] _enabledValidations;

        // Results
        private GenerationResult lastGenerationResult;
        private List<ValidationResult> lastValidationResults;
        private readonly HashSet<int> problematicLevels = new();

        // Levels Tab
        private List<LevelsSO> _allLevels = new();
        private LevelsSO _selectedLevelForView;
        private Vector2 _matrixScrollPosition;
        private int _selectedMatrixIndex = 0;
        private List<Vector2Int> _highlightedCells = new();
        private bool _isShowingBonusWord = false;
        private int _currentBonusWordIndex = 0;
        private float _highlightTimer = 0f;
        private string _currentHighlightedWord = "";
        private const float HIGHLIGHT_DURATION = 0.8f;
        private string _levelSearchFilter = "";

        // UI Styles
        private GUIStyle headerStyle;
        private GUIStyle tabStyle;
        private GUIStyle activeTabStyle;
        private GUIStyle sectionHeaderStyle;
        private GUIStyle cardStyle;
        private GUIStyle buttonPrimaryStyle;
        private GUIStyle buttonSecondaryStyle;
        private bool stylesInitialized = false;

        // Bonus Words
        private LevelsSO _bonusWordsLevel;
        private List<BonusWordInfo> _foundBonusWords;
        private bool _isSearchingBonusWords = false;
        private float _bonusWordsProgress = 0f;
        private string _currentProcessingLevel = "";
        private int _processedLevelsCount = 0;
        private int _totalLevelsCount = 0;

        public static void ShowWindow()
        {
            var window = GetWindow<LevelEditorWindow>("Level Editor");
            window.maximized = true;
            window.Show();
        }

        private void OnEnable()
        {
            InitializeServices();
        }

        private void InitializeServices()
        {
            generationService = new LevelGenerationService();
            bonusWordsService = new BonusWordsDetectionService(validationSettings);

            validationServices = new List<IValidationService>
            {
                new BadWordValidationServiceEditor(),
                new MatrixIntegrityValidationService(),
                new MatrixUniquenessValidationService(),
                new SingleLineCollectionValidationService()
            };

            _enabledValidations = new bool[validationServices.Count];
            for (int i = 0; i < _enabledValidations.Length; i++)
            {
                _enabledValidations[i] = true;
            }

            // Load WordsData
            LoadWordsData();
        }

        private void LoadWordsData()
        {
            wordsData = AssetDatabase.LoadAssetAtPath<WordsData>("Assets/_Project/Prefabs/SO/tables/WordsData.asset");
            if (wordsData == null)
            {
                Debug.LogError("WordsData not found at: Assets/_Project/Prefabs/SO/tables/WordsData.asset");
            }
        }

        private void OnGUI()
        {
            InitializeStyles();

            // Main container
            EditorGUILayout.BeginVertical(GUILayout.ExpandHeight(true));

            DrawModernHeader();
            DrawModernTabs();

            // Content area с правильным скроллом
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.ExpandHeight(true));

            EditorGUILayout.BeginHorizontal();
            GUILayout.Space(20);
            EditorGUILayout.BeginVertical();
            GUILayout.Space(15);

            switch (currentTab)
            {
                case Tab.Generation:
                    DrawGenerationTab();
                    break;
                case Tab.Validation:
                    DrawValidationTab();
                    break;
                case Tab.Levels:
                    DrawLevelsTabContent(); // Специальная версия без собственного скролла
                    break;
                case Tab.WordsData:
                    DrawWordsDataTab();
                    break;
                case Tab.Settings:
                    DrawSettingsTab();
                    break;
                case Tab.Help:
                    DrawHelpTab();
                    break;
            }

            GUILayout.Space(20);
            EditorGUILayout.EndVertical();
            GUILayout.Space(20);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();
        }

        private void InitializeStyles()
        {
            if (stylesInitialized) return;

            // Modern Material Design inspired styles with proper contrast

            // Header style - clean and modern
            headerStyle = new GUIStyle(EditorStyles.largeLabel)
            {
                fontSize = 18,
                fontStyle = FontStyle.Normal,
                alignment = TextAnchor.MiddleLeft,
                normal = { textColor = EditorGUIUtility.isProSkin ?
                    new Color(0.9f, 0.9f, 0.9f) : new Color(0.15f, 0.15f, 0.15f) },
                padding = new RectOffset(0, 0, 12, 8),
                margin = new RectOffset(0, 0, 0, 8)
            };

            // Tab styles - modern flat design
            tabStyle = new GUIStyle()
            {
                fontSize = 12,
                fontStyle = FontStyle.Normal,
                fixedHeight = 36,
                alignment = TextAnchor.MiddleCenter,
                padding = new RectOffset(20, 20, 8, 8),
                margin = new RectOffset(0, 1, 0, 0),
                border = new RectOffset(0, 0, 0, 2),
                normal = {
                    background = EditorGUIUtility.isProSkin ?
                        MakeTexture(new Color(0.25f, 0.25f, 0.25f, 1f)) :
                        MakeTexture(new Color(0.95f, 0.95f, 0.95f, 1f)),
                    textColor = EditorGUIUtility.isProSkin ?
                        new Color(0.7f, 0.7f, 0.7f) : new Color(0.5f, 0.5f, 0.5f)
                },
                hover = {
                    background = EditorGUIUtility.isProSkin ?
                        MakeTexture(new Color(0.28f, 0.28f, 0.28f, 1f)) :
                        MakeTexture(new Color(0.92f, 0.92f, 0.92f, 1f)),
                    textColor = EditorGUIUtility.isProSkin ?
                        new Color(0.85f, 0.85f, 0.85f) : new Color(0.3f, 0.3f, 0.3f)
                }
            };

            activeTabStyle = new GUIStyle(tabStyle)
            {
                fontStyle = FontStyle.Normal,
                normal = {
                    background = EditorGUIUtility.isProSkin ?
                        MakeTexture(new Color(0.32f, 0.32f, 0.32f, 1f)) :
                        MakeTexture(new Color(1f, 1f, 1f, 1f)),
                    textColor = EditorGUIUtility.isProSkin ?
                        new Color(0.95f, 0.95f, 0.95f) : new Color(0.15f, 0.15f, 0.15f)
                },
                hover = {
                    background = EditorGUIUtility.isProSkin ?
                        MakeTexture(new Color(0.32f, 0.32f, 0.32f, 1f)) :
                        MakeTexture(new Color(1f, 1f, 1f, 1f)),
                    textColor = EditorGUIUtility.isProSkin ?
                        new Color(0.95f, 0.95f, 0.95f) : new Color(0.15f, 0.15f, 0.15f)
                }
            };

            // Section header - modern typography
            sectionHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 13,
                fontStyle = FontStyle.Bold,
                padding = new RectOffset(0, 0, 0, 6),
                margin = new RectOffset(0, 0, 0, 8),
                normal = { textColor = EditorGUIUtility.isProSkin ?
                    new Color(0.85f, 0.85f, 0.85f) : new Color(0.25f, 0.25f, 0.25f) }
            };

            // Card style - clean containers with subtle shadows
            cardStyle = new GUIStyle()
            {
                padding = new RectOffset(16, 16, 16, 16),
                margin = new RectOffset(0, 0, 0, 12),
                normal = { background = EditorGUIUtility.isProSkin ?
                    MakeTexture(new Color(0.28f, 0.28f, 0.28f, 1f)) :
                    MakeTexture(new Color(0.98f, 0.98f, 0.98f, 1f)) },
                border = new RectOffset(1, 1, 1, 1)
            };

            // Primary button - modern flat design
            buttonPrimaryStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 12,
                fontStyle = FontStyle.Normal,
                padding = new RectOffset(16, 16, 10, 10),
                fixedHeight = 34,
                border = new RectOffset(1, 1, 1, 1),
                normal = {
                    background = EditorGUIUtility.isProSkin ?
                        MakeTexture(new Color(0.35f, 0.35f, 0.35f, 1f)) :
                        MakeTexture(new Color(0.88f, 0.88f, 0.88f, 1f)),
                    textColor = EditorGUIUtility.isProSkin ?
                        new Color(0.9f, 0.9f, 0.9f) : new Color(0.2f, 0.2f, 0.2f)
                },
                hover = {
                    background = EditorGUIUtility.isProSkin ?
                        MakeTexture(new Color(0.4f, 0.4f, 0.4f, 1f)) :
                        MakeTexture(new Color(0.82f, 0.82f, 0.82f, 1f)),
                    textColor = EditorGUIUtility.isProSkin ?
                        new Color(0.95f, 0.95f, 0.95f) : new Color(0.15f, 0.15f, 0.15f)
                },
                active = {
                    background = EditorGUIUtility.isProSkin ?
                        MakeTexture(new Color(0.3f, 0.3f, 0.3f, 1f)) :
                        MakeTexture(new Color(0.78f, 0.78f, 0.78f, 1f))
                }
            };

            // Secondary button - smaller and subtle
            buttonSecondaryStyle = new GUIStyle(GUI.skin.button)
            {
                fontSize = 11,
                fontStyle = FontStyle.Normal,
                padding = new RectOffset(12, 12, 8, 8),
                fixedHeight = 30,
                border = new RectOffset(1, 1, 1, 1),
                normal = {
                    background = EditorGUIUtility.isProSkin ?
                        MakeTexture(new Color(0.3f, 0.3f, 0.3f, 1f)) :
                        MakeTexture(new Color(0.92f, 0.92f, 0.92f, 1f)),
                    textColor = EditorGUIUtility.isProSkin ?
                        new Color(0.8f, 0.8f, 0.8f) : new Color(0.3f, 0.3f, 0.3f)
                },
                hover = {
                    background = EditorGUIUtility.isProSkin ?
                        MakeTexture(new Color(0.35f, 0.35f, 0.35f, 1f)) :
                        MakeTexture(new Color(0.88f, 0.88f, 0.88f, 1f))
                }
            };

            stylesInitialized = true;
        }

        private void DrawModernHeader()
        {
            // Header container with subtle background
            var headerBg = EditorGUIUtility.isProSkin ?
                MakeTexture(new Color(0.22f, 0.22f, 0.22f, 1f)) :
                MakeTexture(new Color(0.96f, 0.96f, 0.96f, 1f));

            var headerContainerStyle = new GUIStyle()
            {
                normal = { background = headerBg },
                padding = new RectOffset(20, 20, 16, 12),
                margin = new RectOffset(0, 0, 0, 0)
            };

            EditorGUILayout.BeginVertical(headerContainerStyle);

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Level Editor", headerStyle);
            GUILayout.FlexibleSpace();

            // Add version or status info
            var versionStyle = new GUIStyle(EditorStyles.miniLabel)
            {
                normal = { textColor = EditorGUIUtility.isProSkin ?
                    new Color(0.6f, 0.6f, 0.6f) : new Color(0.5f, 0.5f, 0.5f) },
                alignment = TextAnchor.MiddleRight
            };
            EditorGUILayout.LabelField("v2.0", versionStyle, GUILayout.Width(40));
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();
        }

        private void DrawModernTabs()
        {
            // Tab container with clean background
            var tabBg = EditorGUIUtility.isProSkin ?
                MakeTexture(new Color(0.24f, 0.24f, 0.24f, 1f)) :
                MakeTexture(new Color(0.94f, 0.94f, 0.94f, 1f));

            var tabContainerStyle = new GUIStyle()
            {
                normal = { background = tabBg },
                padding = new RectOffset(20, 20, 0, 0),
                margin = new RectOffset(0, 0, 0, 0)
            };

            EditorGUILayout.BeginVertical(tabContainerStyle);
            EditorGUILayout.BeginHorizontal();

            var tabs = new[] {
                ("🔧 Generation", Tab.Generation),
                ("✓ Validation", Tab.Validation),
                ("📋 Levels", Tab.Levels),
                ("📝 Words Data", Tab.WordsData),
                ("⚙️ Settings", Tab.Settings),
                ("❓ Help", Tab.Help)
            };

            foreach (var (name, tab) in tabs)
            {
                var style = currentTab == tab ? activeTabStyle : tabStyle;
                if (GUILayout.Toggle(currentTab == tab, name, style))
                {
                    currentTab = tab;
                }
            }

            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();
        }



        private void DrawGenerationTab()
        {
            // Main workflow section
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("Generation Workflow", sectionHeaderStyle);

            var workflowStyle = new GUIStyle(EditorStyles.helpBox)
            {
                padding = new RectOffset(12, 12, 8, 8),
                margin = new RectOffset(0, 0, 8, 12)
            };

            EditorGUILayout.BeginVertical(workflowStyle);
            EditorGUILayout.LabelField("1. Create Word Distribution → 2. Generate Levels → 3. Validate Results", EditorStyles.miniLabel);
            EditorGUILayout.EndVertical();

            // Step 1: Word Distribution
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Step 1:", GUILayout.Width(50));
            if (GUILayout.Button("Create Word Distribution", buttonPrimaryStyle, GUILayout.Height(36)))
            {
                CreateWordDistributionAsync().Forget();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(8);

            // Step 2: Level Generation
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Step 2:", GUILayout.Width(50));
            EditorGUILayout.BeginVertical();

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Generate All Levels", buttonPrimaryStyle, GUILayout.Height(36)))
            {
                GenerateAllLevelsAsync().Forget();
            }

            GUILayout.Space(8);

            EditorGUILayout.BeginVertical(GUILayout.Width(140));
            levelToGenerate = EditorGUILayout.IntField("Level #", levelToGenerate);
            if (GUILayout.Button("Generate Single", buttonSecondaryStyle))
            {
                GenerateOneLevelAsync(levelToGenerate).Forget();
            }
            EditorGUILayout.EndVertical();
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();

            // Results Section
            if (lastGenerationResult != null)
            {
                EditorGUILayout.Space(12);
                DrawGenerationResults();
            }

            // Bonus Words Section
            EditorGUILayout.Space(12);
            DrawBonusWordsSection();
        }

        private void DrawValidationTab()
        {
            // Level Selection Section
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("Level Selection", sectionHeaderStyle);

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Target Level:", GUILayout.Width(80));
            selectedLevel = (LevelsSO)EditorGUILayout.ObjectField(selectedLevel, typeof(LevelsSO), false);
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(12);

            // Validation Services Section
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("Validation Services", sectionHeaderStyle);

            for (int i = 0; i < validationServices.Count; i++)
            {
                EditorGUILayout.BeginHorizontal();
                _enabledValidations[i] = EditorGUILayout.Toggle(_enabledValidations[i], GUILayout.Width(20));
                EditorGUILayout.LabelField(validationServices[i].ServiceName, GUILayout.ExpandWidth(true));
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.Space(2);
            }
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(12);

            // Actions Section
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("Validation Actions", sectionHeaderStyle);

            EditorGUILayout.BeginHorizontal();
            EditorGUI.BeginDisabledGroup(selectedLevel == null);
            if (GUILayout.Button("Validate Selected Level", buttonSecondaryStyle, GUILayout.Height(36)))
            {
                ValidateSelectedLevelAsync().Forget();
            }
            EditorGUI.EndDisabledGroup();

            GUILayout.Space(8);

            if (GUILayout.Button("Validate All Levels", buttonPrimaryStyle, GUILayout.Height(36)))
            {
                ValidateAllLevelsAsync().Forget();
            }
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();

            // Results Section
            if (lastValidationResults != null && lastValidationResults.Count > 0)
            {
                EditorGUILayout.Space(12);
                DrawValidationResults();
            }
        }

        private void DrawBonusWordsSection()
        {
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("Bonus Words Detection", sectionHeaderStyle);

            var infoStyle = new GUIStyle(EditorStyles.helpBox)
            {
                padding = new RectOffset(12, 12, 8, 8),
                margin = new RectOffset(0, 0, 0, 12),
                fontSize = 11
            };

            EditorGUILayout.BeginVertical(infoStyle);
            EditorGUILayout.LabelField("Finds additional words players can collect using snake-like movement (↑↓←→ only)", EditorStyles.wordWrappedMiniLabel);
            EditorGUILayout.EndVertical();

            // Single level analysis
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Analyze Single Level:", GUILayout.Width(130));
            _bonusWordsLevel = (LevelsSO)EditorGUILayout.ObjectField(_bonusWordsLevel, typeof(LevelsSO), false);

            EditorGUI.BeginDisabledGroup(_bonusWordsLevel == null || _isSearchingBonusWords);
            if (GUILayout.Button("Analyze", buttonSecondaryStyle, GUILayout.Width(80)))
            {
                FindBonusWordsAsync().Forget();
            }
            EditorGUI.EndDisabledGroup();
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(8);

            // Batch analysis
            EditorGUI.BeginDisabledGroup(_isSearchingBonusWords);
            if (GUILayout.Button("Analyze All Levels", buttonPrimaryStyle, GUILayout.Height(36)))
            {
                FindBonusWordsForAllLevelsAsync().Forget();
            }
            EditorGUI.EndDisabledGroup();

            // Progress indicator
            if (_isSearchingBonusWords)
            {
                EditorGUILayout.Space(12);

                string progressText;
                if (_bonusWordsLevel != null)
                {
                    progressText = $"Analyzing Level {_bonusWordsLevel.LevelNum}...";
                }
                else if (!string.IsNullOrEmpty(_currentProcessingLevel))
                {
                    progressText = $"Processing {_currentProcessingLevel} ({_processedLevelsCount}/{_totalLevelsCount}) - {_bonusWordsProgress * 100:F0}%";
                }
                else
                {
                    progressText = $"Processing all levels... {_bonusWordsProgress * 100:F0}%";
                }

                var progressRect = EditorGUILayout.GetControlRect(false, 24);
                EditorGUI.ProgressBar(progressRect, _bonusWordsProgress, progressText);

                // Дополнительная информация
                if (_totalLevelsCount > 0)
                {
                    EditorGUILayout.LabelField($"Progress: {_processedLevelsCount}/{_totalLevelsCount} levels completed", EditorStyles.miniLabel);
                }
            }

            // Results display
            if (_foundBonusWords != null)
            {
                EditorGUILayout.Space(12);
                DrawBonusWordsResults();
            }

            EditorGUILayout.EndVertical();
        }



        private void DrawLevelsTabContent()
        {
            // Header with controls
            var headerStyle = new GUIStyle()
            {
                padding = new RectOffset(0, 0, 0, 12),
                margin = new RectOffset(0, 0, 0, 0)
            };

            EditorGUILayout.BeginVertical(headerStyle);
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Level Browser", sectionHeaderStyle);
            GUILayout.FlexibleSpace();

            // Modern search bar
            EditorGUILayout.LabelField("🔍", GUILayout.Width(20));
            var searchStyle = new GUIStyle(EditorStyles.textField)
            {
                fontSize = 11,
                padding = new RectOffset(8, 8, 6, 6)
            };
            _levelSearchFilter = EditorGUILayout.TextField(_levelSearchFilter, searchStyle, GUILayout.Width(160), GUILayout.Height(24));

            if (!string.IsNullOrEmpty(_levelSearchFilter) && GUILayout.Button("✕", GUILayout.Width(24), GUILayout.Height(24)))
            {
                _levelSearchFilter = "";
                GUI.FocusControl(null);
            }

            GUILayout.Space(8);

            if (GUILayout.Button("📁 Load Levels", buttonSecondaryStyle, GUILayout.Width(100)))
            {
                LoadAllLevels();
            }
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();

            // Main content area with improved layout
            EditorGUILayout.BeginHorizontal();

            // Left panel - Levels List
            var leftPanelStyle = new GUIStyle(cardStyle)
            {
                padding = new RectOffset(12, 12, 12, 12)
            };
            EditorGUILayout.BeginVertical(leftPanelStyle, GUILayout.Width(320));
            DrawLevelsListContent();
            EditorGUILayout.EndVertical();

            GUILayout.Space(12);

            // Right panel - Level Details
            var rightPanelStyle = new GUIStyle(cardStyle)
            {
                padding = new RectOffset(16, 16, 16, 16)
            };
            EditorGUILayout.BeginVertical(rightPanelStyle);
            if (_selectedLevelForView != null)
            {
                DrawLevelDetailsContent();
            }
            else
            {
                // Empty state
                EditorGUILayout.BeginVertical();
                GUILayout.FlexibleSpace();
                EditorGUILayout.BeginHorizontal();
                GUILayout.FlexibleSpace();

                var emptyStateStyle = new GUIStyle(EditorStyles.centeredGreyMiniLabel)
                {
                    fontSize = 12,
                    normal = { textColor = EditorGUIUtility.isProSkin ?
                        new Color(0.6f, 0.6f, 0.6f) : new Color(0.5f, 0.5f, 0.5f) }
                };
                EditorGUILayout.LabelField("📋 Select a level to view details", emptyStateStyle);

                GUILayout.FlexibleSpace();
                EditorGUILayout.EndHorizontal();
                GUILayout.FlexibleSpace();
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndVertical();

            EditorGUILayout.EndHorizontal();
        }

        private void DrawGenerationResults()
        {
            EditorGUILayout.BeginVertical(cardStyle);

            // Status header with icon
            EditorGUILayout.BeginHorizontal();
            var statusIcon = lastGenerationResult.IsSuccess ? "✓" : "✗";
            var statusColor = lastGenerationResult.IsSuccess ?
                (EditorGUIUtility.isProSkin ? new Color(0.6f, 0.9f, 0.6f) : new Color(0.2f, 0.6f, 0.2f)) :
                (EditorGUIUtility.isProSkin ? new Color(0.9f, 0.6f, 0.6f) : new Color(0.8f, 0.2f, 0.2f));

            var statusStyle = new GUIStyle(sectionHeaderStyle)
            {
                normal = { textColor = statusColor }
            };

            EditorGUILayout.LabelField($"{statusIcon} Generation Results", statusStyle);
            EditorGUILayout.EndHorizontal();

            // Main result message
            var resultStyle = new GUIStyle(EditorStyles.helpBox)
            {
                padding = new RectOffset(12, 12, 8, 8),
                margin = new RectOffset(0, 0, 8, 8)
            };

            EditorGUILayout.BeginVertical(resultStyle);
            EditorGUILayout.LabelField(lastGenerationResult.Message, EditorStyles.wordWrappedLabel);
            EditorGUILayout.EndVertical();

            // Errors section
            if (lastGenerationResult.Errors.Count > 0)
            {
                EditorGUILayout.LabelField($"Errors ({lastGenerationResult.Errors.Count}):", EditorStyles.boldLabel);
                foreach (var error in lastGenerationResult.Errors)
                {
                    EditorGUILayout.HelpBox($"• {error}", MessageType.Error);
                }
            }

            // Warnings section
            if (lastGenerationResult.Warnings.Count > 0)
            {
                EditorGUILayout.LabelField($"Warnings ({lastGenerationResult.Warnings.Count}):", EditorStyles.boldLabel);
                foreach (var warning in lastGenerationResult.Warnings)
                {
                    EditorGUILayout.HelpBox($"• {warning}", MessageType.Warning);
                }
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawValidationResults()
        {
            EditorGUILayout.BeginVertical(cardStyle);

            // Results header
            int failedCount = lastValidationResults.Count;
            var statusIcon = failedCount == 0 ? "✓" : "⚠";
            var statusColor = failedCount == 0 ?
                (EditorGUIUtility.isProSkin ? new Color(0.6f, 0.9f, 0.6f) : new Color(0.2f, 0.6f, 0.2f)) :
                (EditorGUIUtility.isProSkin ? new Color(0.9f, 0.8f, 0.4f) : new Color(0.8f, 0.6f, 0.1f));

            var statusStyle = new GUIStyle(sectionHeaderStyle)
            {
                normal = { textColor = statusColor }
            };

            EditorGUILayout.LabelField($"{statusIcon} Validation Results", statusStyle);

            // Summary
            if (failedCount == 0)
            {
                var successStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    padding = new RectOffset(12, 12, 8, 8),
                    margin = new RectOffset(0, 0, 8, 8)
                };

                EditorGUILayout.BeginVertical(successStyle);
                EditorGUILayout.LabelField("All validations passed successfully", EditorStyles.wordWrappedLabel);
                EditorGUILayout.EndVertical();
            }
            else
            {
                var warningStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    padding = new RectOffset(12, 12, 8, 8),
                    margin = new RectOffset(0, 0, 8, 8)
                };

                EditorGUILayout.BeginVertical(warningStyle);
                EditorGUILayout.LabelField($"Found {failedCount} validation issue(s) that need attention", EditorStyles.wordWrappedLabel);
                EditorGUILayout.EndVertical();

                // Regeneration section
                if (problematicLevels.Count > 0)
                {
                    EditorGUILayout.Space(8);

                    if (GUILayout.Button($"🔄 Regenerate {problematicLevels.Count} Problematic Level(s)", buttonPrimaryStyle, GUILayout.Height(36)))
                    {
                        RegenerateProblematicLevelsAsync().Forget();
                    }

                    // Show affected levels
                    var levelsList = problematicLevels.OrderBy(x => x).ToArray();
                    string levelsText = levelsList.Length <= 10 ?
                        string.Join(", ", levelsList) :
                        $"{string.Join(", ", levelsList.Take(10))} and {levelsList.Length - 10} more";

                    var levelsInfoStyle = new GUIStyle(EditorStyles.helpBox)
                    {
                        padding = new RectOffset(12, 12, 6, 6),
                        margin = new RectOffset(0, 0, 8, 8)
                    };

                    EditorGUILayout.BeginVertical(levelsInfoStyle);
                    EditorGUILayout.LabelField($"Affected levels: {levelsText}", EditorStyles.wordWrappedMiniLabel);
                    EditorGUILayout.EndVertical();
                }

                // Issues details
                EditorGUILayout.Space(8);
                EditorGUILayout.LabelField("Issue Details:", EditorStyles.boldLabel);

                foreach (var result in lastValidationResults)
                {
                    var issueStyle = new GUIStyle(EditorStyles.helpBox)
                    {
                        padding = new RectOffset(12, 12, 8, 8),
                        margin = new RectOffset(0, 0, 4, 4)
                    };

                    EditorGUILayout.BeginVertical(issueStyle);
                    EditorGUILayout.LabelField(result.Summary, EditorStyles.boldLabel);

                    if (result.Errors.Count > 0)
                    {
                        foreach (var error in result.Errors)
                        {
                            string errorMessage = $"• {error.ErrorType}: {error.Description}";
                            if (!string.IsNullOrEmpty(error.AdditionalInfo))
                            {
                                errorMessage += $" ({error.AdditionalInfo})";
                            }
                            EditorGUILayout.LabelField(errorMessage, EditorStyles.wordWrappedMiniLabel);
                        }
                    }
                    EditorGUILayout.EndVertical();
                }
            }

            EditorGUILayout.EndVertical();
        }

        private async UniTaskVoid CreateWordDistributionAsync()
        {
            var progressWindow = ProgressWindow.ShowProgress("Creating Word Distribution");

            try
            {
                // Update generation settings
                LevelGenerationService.PreventStraightLines = preventStraightLines;
                LevelGenerationService.BendPriority = bendPriority;
                LevelGenerationService.FastGeneration = fastGeneration;

                UpdateGenerationSettings();

                var progress = new Progress<GenerationProgress>(p =>
                {
                    progressWindow.UpdateProgress(p);
                });

                lastGenerationResult = await generationService.CreateWordDistributionAsync(progress);

                progressWindow.SetCompleted(lastGenerationResult.IsSuccess, lastGenerationResult.Message);

                // Clear validation results when generating new distribution
                lastValidationResults?.Clear();
                problematicLevels.Clear();

                Repaint();
            }
            catch (Exception ex)
            {
                progressWindow.SetCompleted(false, $"Error: {ex.Message}");
                Debug.LogError($"Word distribution creation failed: {ex}");
            }
        }

        private async UniTaskVoid GenerateAllLevelsAsync()
        {
            var progressWindow = ProgressWindow.ShowProgress("Generating All Levels");

            try
            {
                // Update generation settings
                LevelGenerationService.PreventStraightLines = preventStraightLines;
                LevelGenerationService.BendPriority = bendPriority;
                LevelGenerationService.FastGeneration = fastGeneration;

                UpdateGenerationSettings();

                var progress = new Progress<GenerationProgress>(p =>
                {
                    progressWindow.UpdateProgress(p);
                });

                lastGenerationResult = await generationService.GenerateAllLevelsAsync(progress);

                progressWindow.SetCompleted(lastGenerationResult.IsSuccess, lastGenerationResult.Message);

                // Clear validation results when generating new levels
                lastValidationResults?.Clear();
                problematicLevels.Clear();

                Repaint();
            }
            catch (Exception ex)
            {
                progressWindow.SetCompleted(false, $"Error: {ex.Message}");
                Debug.LogError($"Level generation failed: {ex}");
            }
        }

        private async UniTaskVoid GenerateOneLevelAsync(int levelNumber)
        {
            var progressWindow = ProgressWindow.ShowProgress($"Generating Level {levelNumber}");

            try
            {
                // Update generation settings
                LevelGenerationService.PreventStraightLines = preventStraightLines;
                LevelGenerationService.BendPriority = bendPriority;
                LevelGenerationService.FastGeneration = fastGeneration;

                UpdateGenerationSettings();

                var progress = new Progress<GenerationProgress>(p =>
                {
                    progressWindow.UpdateProgress(p);
                });

                lastGenerationResult = await generationService.GenerateOneLevelAsync(levelNumber, progress);

                progressWindow.SetCompleted(lastGenerationResult.IsSuccess, lastGenerationResult.Message);

                // Remove this level from problematic levels if generation was successful
                if (lastGenerationResult.IsSuccess)
                {
                    problematicLevels.Remove(levelNumber);
                }

                Repaint();
            }
            catch (Exception ex)
            {
                progressWindow.SetCompleted(false, $"Error: {ex.Message}");
                Debug.LogError($"Level generation failed: {ex}");
            }
        }

        private async UniTaskVoid ValidateSelectedLevelAsync()
        {
            if (selectedLevel == null) return;

            var progressWindow = ProgressWindow.ShowProgress($"Validating Level {selectedLevel.LevelNum}");

            try
            {
                lastValidationResults = new List<ValidationResult>();
                int totalValidations = 0;

                for (int i = 0; i < validationServices.Count; i++)
                {
                    if (!_enabledValidations[i]) continue;

                    var service = validationServices[i];
                    var progress = new Progress<ValidationProgress>(p =>
                    {
                        progressWindow.UpdateProgress(p);
                    });

                    var result = await service.ValidateAsync(selectedLevel, progress);
                    totalValidations++;

                    // Only add failed validations to results
                    if (!result.IsValid)
                    {
                        result.Summary = $"Level {selectedLevel.LevelNum} - {result.Summary}";
                        lastValidationResults.Add(result);
                    }
                }

                int failedCount = lastValidationResults.Count;
                bool allValid = failedCount == 0;
                string message = allValid ?
                    $"All {totalValidations} validations passed" :
                    $"{failedCount} validation(s) failed";

                progressWindow.SetCompleted(allValid, message);
                ExtractProblematicLevels();
                Repaint();
            }
            catch (Exception ex)
            {
                progressWindow.SetCompleted(false, $"Error: {ex.Message}");
                Debug.LogError($"Validation failed: {ex}");
            }
        }

        private async UniTaskVoid ValidateAllLevelsAsync()
        {
            var progressWindow = ProgressWindow.ShowProgress("Validating All Levels");

            try
            {
                // Load all levels from LevelsConfigSO
                var levelsConfig = AssetDatabase.LoadAssetAtPath<LevelsConfigSO>("Assets/_Project/Prefabs/SO/LevelsConfig/LevelsConfig.asset");
                if (levelsConfig == null || levelsConfig.Levels == null || levelsConfig.Levels.Count == 0)
                {
                    progressWindow.SetCompleted(false, "No levels found in LevelsConfigSO");
                    return;
                }

                lastValidationResults = new List<ValidationResult>();
                int totalOperations = 0;
                int totalValidations = 0; // Track total validations performed

                // Count enabled validations
                for (int i = 0; i < _enabledValidations.Length; i++)
                {
                    if (_enabledValidations[i]) totalOperations += levelsConfig.Levels.Count;
                }

                int currentOperation = 0;

                foreach (var level in levelsConfig.Levels)
                {
                    for (int i = 0; i < validationServices.Count; i++)
                    {
                        if (!_enabledValidations[i]) continue;

                        var service = validationServices[i];
                        currentOperation++;

                        progressWindow.UpdateProgress(
                            $"Level {level.LevelNum} - {service.ServiceName}",
                            (float)currentOperation / totalOperations
                        );

                        var result = await service.ValidateAsync(level);
                        totalValidations++;

                        // Only add failed validations to results
                        if (!result.IsValid)
                        {
                            result.Summary = $"Level {level.LevelNum} - {result.Summary}";
                            lastValidationResults.Add(result);
                        }
                    }
                }

                int failedCount = lastValidationResults.Count; // All stored results are failures
                int passedCount = totalValidations - failedCount;
                bool allValid = failedCount == 0;
                string message = allValid ?
                    $"All {totalValidations} validations passed" :
                    $"{failedCount} validation(s) failed, {passedCount} passed";

                progressWindow.SetCompleted(allValid, message);
                ExtractProblematicLevels();
                Repaint();
            }
            catch (Exception ex)
            {
                progressWindow.SetCompleted(false, $"Error: {ex.Message}");
                Debug.LogError($"Validation failed: {ex}");
            }
        }

        private void ExtractProblematicLevels()
        {
            problematicLevels.Clear();

            foreach (var result in lastValidationResults)
            {
                // Extract level number from summary like "Level 15 - Bad Words Validation: Found 1 bad word(s)"
                if (result.Summary.StartsWith("Level "))
                {
                    var parts = result.Summary.Split(' ');
                    if (parts.Length > 1 && int.TryParse(parts[1], out int levelNum))
                    {
                        problematicLevels.Add(levelNum);
                        Debug.Log($"Added problematic level: {levelNum} (from: {result.Summary})");
                    }
                    else
                    {
                        Debug.LogWarning($"Could not parse level number from: {result.Summary}");
                    }
                }
                else
                {
                    Debug.LogWarning($"Summary doesn't start with 'Level ': {result.Summary}");
                }
            }

            Debug.Log($"Total problematic levels found: {problematicLevels.Count} - {string.Join(", ", problematicLevels.OrderBy(x => x))}");
        }

        private async UniTaskVoid RegenerateProblematicLevelsAsync()
        {
            if (problematicLevels.Count == 0) return;

            var progressWindow = ProgressWindow.ShowProgress($"Regenerating {problematicLevels.Count} Problematic Levels");

            try
            {
                // Update generation settings
                LevelGenerationService.PreventStraightLines = preventStraightLines;
                LevelGenerationService.BendPriority = bendPriority;
                LevelGenerationService.FastGeneration = fastGeneration;

                UpdateGenerationSettings();

                var levelsList = problematicLevels.OrderBy(x => x).ToList();
                int processedCount = 0;
                var errors = new List<string>();

                foreach (int levelNum in levelsList)
                {
                    progressWindow.UpdateProgress($"Regenerating level {levelNum}", (float)processedCount / levelsList.Count);

                    var progress = new Progress<GenerationProgress>(p =>
                    {
                        progressWindow.AddLogMessage($"Level {levelNum}: {p.CurrentOperation}");
                    });

                    var result = await generationService.GenerateOneLevelAsync(levelNum, progress);

                    if (!result.IsSuccess)
                    {
                        errors.AddRange(result.Errors);
                        progressWindow.AddLogMessage($"Failed to regenerate level {levelNum}");
                    }
                    else
                    {
                        progressWindow.AddLogMessage($"Successfully regenerated level {levelNum}");
                    }

                    processedCount++;
                }

                string message = errors.Count == 0 ?
                    $"Successfully regenerated all {levelsList.Count} levels" :
                    $"Regenerated {levelsList.Count - errors.Count}/{levelsList.Count} levels with {errors.Count} errors";

                progressWindow.SetCompleted(errors.Count == 0, message);

                // Clear problematic levels since we regenerated them
                problematicLevels.Clear();

                Repaint();
            }
            catch (Exception ex)
            {
                progressWindow.SetCompleted(false, $"Error: {ex.Message}");
                Debug.LogError($"Regeneration failed: {ex}");
            }
        }

        private void DrawSettingsTab()
        {
            // Matrix Generation Settings
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("Matrix Generation", sectionHeaderStyle);

            var fieldStyle = new GUIStyle(EditorStyles.numberField)
            {
                fontSize = 11
            };

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Max Attempts Per Matrix:", GUILayout.Width(160));
            maxAttemptsPerMatrix = EditorGUILayout.IntField(maxAttemptsPerMatrix, fieldStyle, GUILayout.Width(80));
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Matrices Per Level:", GUILayout.Width(160));
            generateCount = EditorGUILayout.IntField(generateCount, fieldStyle, GUILayout.Width(80));
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(8);

            // Snake pattern settings
            EditorGUILayout.BeginHorizontal();
            preventStraightLines = EditorGUILayout.Toggle(preventStraightLines, GUILayout.Width(20));
            EditorGUILayout.LabelField("🐍 Prevent Straight Line Words", GUILayout.ExpandWidth(true));
            EditorGUILayout.EndHorizontal();

            if (preventStraightLines)
            {
                var indentStyle = new GUIStyle()
                {
                    padding = new RectOffset(20, 0, 8, 8),
                    margin = new RectOffset(0, 0, 0, 8)
                };

                EditorGUILayout.BeginVertical(indentStyle);

                EditorGUILayout.BeginHorizontal();
                fastGeneration = EditorGUILayout.Toggle(fastGeneration, GUILayout.Width(20));
                EditorGUILayout.LabelField("⚡ Fast Generation Mode", GUILayout.ExpandWidth(true));
                EditorGUILayout.EndHorizontal();

                if (!fastGeneration)
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("Bend Priority:", GUILayout.Width(100));
                    bendPriority = EditorGUILayout.Slider(bendPriority, 0.1f, 1.0f);
                    EditorGUILayout.EndHorizontal();

                    var infoStyle = new GUIStyle(EditorStyles.helpBox)
                    {
                        fontSize = 10,
                        padding = new RectOffset(8, 8, 6, 6)
                    };
                    EditorGUILayout.BeginVertical(infoStyle);
                    EditorGUILayout.LabelField($"Higher values ({bendPriority:F1}) create more snake-like patterns but slower generation", EditorStyles.wordWrappedMiniLabel);
                    EditorGUILayout.EndVertical();
                }
                else
                {
                    var infoStyle = new GUIStyle(EditorStyles.helpBox)
                    {
                        fontSize = 10,
                        padding = new RectOffset(8, 8, 6, 6)
                    };
                    EditorGUILayout.BeginVertical(infoStyle);
                    EditorGUILayout.LabelField("Optimized for speed with moderate snake patterns. Recommended for large level generation.", EditorStyles.wordWrappedMiniLabel);
                    EditorGUILayout.EndVertical();
                }

                EditorGUILayout.EndVertical();
            }
            else
            {
                var infoStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    fontSize = 10,
                    padding = new RectOffset(8, 8, 6, 6)
                };
                EditorGUILayout.BeginVertical(infoStyle);
                EditorGUILayout.LabelField("When enabled, words will be forced to have bends/turns and cannot be collected in straight lines", EditorStyles.wordWrappedMiniLabel);
                EditorGUILayout.EndVertical();
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(12);

            // Extended Levels Settings
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("Extended Levels", sectionHeaderStyle);

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("From What Level Repeat:", GUILayout.Width(160));
            fromWhatLevelRepeat = EditorGUILayout.IntField(fromWhatLevelRepeat, fieldStyle, GUILayout.Width(80));
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Reference Range Start:", GUILayout.Width(160));
            fromStart = EditorGUILayout.IntField(fromStart, fieldStyle, GUILayout.Width(80));
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Reference Range End:", GUILayout.Width(160));
            toEnd = EditorGUILayout.IntField(toEnd, fieldStyle, GUILayout.Width(80));
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(12);

            // Generation Conditions
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("Generation Conditions", sectionHeaderStyle);

            var conditionsInfoStyle = new GUIStyle(EditorStyles.helpBox)
            {
                fontSize = 10,
                padding = new RectOffset(8, 8, 6, 6),
                margin = new RectOffset(0, 0, 0, 8)
            };
            EditorGUILayout.BeginVertical(conditionsInfoStyle);
            EditorGUILayout.LabelField("Enable or disable specific checks during level generation. Disabling checks can speed up generation but may reduce quality.", EditorStyles.wordWrappedMiniLabel);
            EditorGUILayout.EndVertical();

            // Validation toggles with modern styling
            DrawValidationToggle("✓ Matrix Uniqueness Check", ref enableMatrixUniquenessCheck,
                "May generate duplicate matrices within levels");

            DrawValidationToggle("🚫 Bad Word Check", ref enableBadWordCheck,
                "May generate matrices containing inappropriate words");

            DrawValidationToggle("🔍 Matrix Integrity Check", ref enableMatrixIntegrityCheck,
                "May generate matrices with missing words or invalid structure");

            DrawValidationToggle("📏 Straight Line Prevention", ref enableStraightLineCheck,
                "Words may be placed in straight lines");

            EditorGUILayout.Space(8);

            DrawValidationToggle("⭐ Strict First Matrix", ref strictFirstMatrix,
                "First matrix may contain straight line words", true);

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(12);

            // Matrix Validation Scopes
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("Matrix Validation Scopes", sectionHeaderStyle);

            var scopeInfoStyle = new GUIStyle(EditorStyles.helpBox)
            {
                fontSize = 10,
                padding = new RectOffset(8, 8, 6, 6),
                margin = new RectOffset(0, 0, 0, 8)
            };
            EditorGUILayout.BeginVertical(scopeInfoStyle);
            EditorGUILayout.LabelField("Configure which matrices each validation service should check. This allows fine-tuning of performance vs quality.", EditorStyles.wordWrappedMiniLabel);
            EditorGUILayout.EndVertical();

            DrawValidationScopeSettings();
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(12);

            // Word Validation Settings
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.BeginHorizontal();
            showWordValidationSettings = EditorGUILayout.Foldout(showWordValidationSettings, "🔍 Word Validation Services", true);
            EditorGUILayout.EndHorizontal();

            if (showWordValidationSettings)
            {
                DrawWordValidationSettings();
            }
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(12);

            // Asset Paths
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("Asset Paths", sectionHeaderStyle);

            var pathStyle = new GUIStyle(EditorStyles.textField)
            {
                fontSize = 10
            };

            DrawPathField("📁 Levels Folder:", ref levelsPath, pathStyle);
            DrawPathField("📄 Word Distribution:", ref wordDistributionPath, pathStyle);
            DrawPathField("📊 Levels Data:", ref levelsDataPath, pathStyle);
            DrawPathField("📝 Words Data:", ref wordsDataPath, pathStyle);
            DrawPathField("⚙️ Levels Config:", ref levelsConfigPath, pathStyle);
            DrawPathField("🚫 Bad Words Data:", ref badWordsPath, pathStyle);

            EditorGUILayout.Space(8);

            // Reset button
            if (GUILayout.Button("🔄 Reset to Defaults", buttonSecondaryStyle, GUILayout.Height(32)))
            {
                ResetSettingsToDefaults();
            }

            EditorGUILayout.Space(8);

            var noteStyle = new GUIStyle(EditorStyles.helpBox)
            {
                fontSize = 10,
                padding = new RectOffset(8, 8, 6, 6)
            };
            EditorGUILayout.BeginVertical(noteStyle);
            EditorGUILayout.LabelField("💡 Changes to settings will take effect after restarting the Level Editor.", EditorStyles.wordWrappedMiniLabel);
            EditorGUILayout.EndVertical();

            EditorGUILayout.EndVertical();
        }

        private void DrawWordValidationSettings()
        {
            var fieldStyle = new GUIStyle(EditorStyles.textField)
            {
                fontSize = 11
            };

            var infoStyle = new GUIStyle(EditorStyles.helpBox)
            {
                fontSize = 10,
                padding = new RectOffset(8, 8, 6, 6),
                margin = new RectOffset(0, 0, 0, 8)
            };

            EditorGUILayout.BeginVertical(infoStyle);
            EditorGUILayout.LabelField("Configure external services for bonus word validation. This allows finding words from 2+ letters using online dictionaries and AI.", EditorStyles.wordWrappedMiniLabel);
            EditorGUILayout.Space(4);

            // Quick links section
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("📚 Quick Links:", EditorStyles.boldLabel, GUILayout.Width(90));

            var quickLinkStyle = new GUIStyle(EditorStyles.miniButton)
            {
                fontSize = 9,
                padding = new RectOffset(4, 4, 2, 2)
            };

            if (GUILayout.Button("Yandex API", quickLinkStyle, GUILayout.Width(80)))
            {
                Application.OpenURL("https://yandex.com/dev/dictionary/");
            }

            if (GUILayout.Button("OpenAI API", quickLinkStyle, GUILayout.Width(80)))
            {
                Application.OpenURL("https://platform.openai.com/api-keys");
            }

            if (GUILayout.Button("📖 Docs", quickLinkStyle, GUILayout.Width(50)))
            {
                Application.OpenURL("https://yandex.com/dev/dictionary/doc/");
            }

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();

            // Local Dictionary
            EditorGUILayout.BeginHorizontal();
            validationSettings.UseLocalDictionary = EditorGUILayout.Toggle(validationSettings.UseLocalDictionary, GUILayout.Width(20));
            EditorGUILayout.LabelField("📚 Use Local Dictionary", GUILayout.ExpandWidth(true));
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(8);

            // Yandex Dictionary
            EditorGUILayout.BeginHorizontal();
            validationSettings.UseYandexDictionary = EditorGUILayout.Toggle(validationSettings.UseYandexDictionary, GUILayout.Width(20));
            EditorGUILayout.LabelField("🔍 Use Yandex Dictionary API", GUILayout.ExpandWidth(true));
            EditorGUILayout.EndHorizontal();

            if (validationSettings.UseYandexDictionary)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("API Key:", GUILayout.Width(80));
                validationSettings.YandexApiKey = EditorGUILayout.TextField(validationSettings.YandexApiKey, fieldStyle);

                // Copy button for API key
                if (!string.IsNullOrEmpty(validationSettings.YandexApiKey))
                {
                    if (GUILayout.Button("📋", GUILayout.Width(25)))
                    {
                        EditorGUIUtility.systemCopyBuffer = validationSettings.YandexApiKey;
                        Debug.Log("📋 Yandex API key copied to clipboard!");
                    }
                }
                EditorGUILayout.EndHorizontal();

                if (string.IsNullOrEmpty(validationSettings.YandexApiKey))
                {
                    EditorGUILayout.HelpBox("⚠️ Yandex Dictionary API key is required.", MessageType.Warning);

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("Get API key:", GUILayout.Width(80));

                    var linkStyle = new GUIStyle(EditorStyles.linkLabel)
                    {
                        fontSize = 11,
                        normal = { textColor = new Color(0.3f, 0.6f, 1f) }
                    };

                    if (GUILayout.Button("🔗 https://yandex.com/dev/dictionary/", linkStyle))
                    {
                        Application.OpenURL("https://yandex.com/dev/dictionary/");
                    }

                    if (GUILayout.Button("📋", GUILayout.Width(25)))
                    {
                        EditorGUIUtility.systemCopyBuffer = "https://yandex.com/dev/dictionary/";
                        Debug.Log("📋 Yandex Dictionary API URL copied to clipboard!");
                    }
                    EditorGUILayout.EndHorizontal();
                }
            }

            EditorGUILayout.Space(8);

            // OpenAI
            EditorGUILayout.BeginHorizontal();
            validationSettings.UseOpenAI = EditorGUILayout.Toggle(validationSettings.UseOpenAI, GUILayout.Width(20));
            EditorGUILayout.LabelField("🤖 Use OpenAI API", GUILayout.ExpandWidth(true));
            EditorGUILayout.EndHorizontal();

            if (validationSettings.UseOpenAI)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("API Key:", GUILayout.Width(80));
                validationSettings.OpenAIApiKey = EditorGUILayout.TextField(validationSettings.OpenAIApiKey, fieldStyle);

                // Copy button for API key
                if (!string.IsNullOrEmpty(validationSettings.OpenAIApiKey))
                {
                    if (GUILayout.Button("📋", GUILayout.Width(25)))
                    {
                        EditorGUIUtility.systemCopyBuffer = validationSettings.OpenAIApiKey;
                        Debug.Log("📋 OpenAI API key copied to clipboard!");
                    }
                }
                EditorGUILayout.EndHorizontal();

                if (string.IsNullOrEmpty(validationSettings.OpenAIApiKey))
                {
                    EditorGUILayout.HelpBox("⚠️ OpenAI API key is required.", MessageType.Warning);

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("Get API key:", GUILayout.Width(80));

                    var linkStyle = new GUIStyle(EditorStyles.linkLabel)
                    {
                        fontSize = 11,
                        normal = { textColor = new Color(0.3f, 0.6f, 1f) }
                    };

                    if (GUILayout.Button("🔗 https://platform.openai.com/api-keys", linkStyle))
                    {
                        Application.OpenURL("https://platform.openai.com/api-keys");
                    }

                    if (GUILayout.Button("📋", GUILayout.Width(25)))
                    {
                        EditorGUIUtility.systemCopyBuffer = "https://platform.openai.com/api-keys";
                        Debug.Log("📋 OpenAI API URL copied to clipboard!");
                    }
                    EditorGUILayout.EndHorizontal();
                }
            }

            EditorGUILayout.Space(8);

            // Advanced Settings
            EditorGUILayout.LabelField("Advanced Settings", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Min Word Length:", GUILayout.Width(120));
            validationSettings.MinWordLengthForExternal = EditorGUILayout.IntSlider(validationSettings.MinWordLengthForExternal, 2, 5, GUILayout.ExpandWidth(true));
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Max Word Length:", GUILayout.Width(120));
            validationSettings.MaxWordLengthForExternal = EditorGUILayout.IntSlider(validationSettings.MaxWordLengthForExternal, 8, 20, GUILayout.ExpandWidth(true));
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Request Timeout:", GUILayout.Width(120));
            validationSettings.RequestTimeoutSeconds = EditorGUILayout.IntSlider(validationSettings.RequestTimeoutSeconds, 5, 30, GUILayout.ExpandWidth(true));
            EditorGUILayout.LabelField("sec", GUILayout.Width(30));
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            validationSettings.EnableCaching = EditorGUILayout.Toggle(validationSettings.EnableCaching, GUILayout.Width(20));
            EditorGUILayout.LabelField("💾 Enable Caching", GUILayout.ExpandWidth(true));
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(8);

            // Apply Settings Button
            if (GUILayout.Button("🔄 Apply Settings", buttonSecondaryStyle, GUILayout.Height(32)))
            {
                ApplyWordValidationSettings();
            }

            // Statistics and Controls
            if (bonusWordsService != null)
            {
                EditorGUILayout.Space(8);
                EditorGUILayout.BeginVertical(infoStyle);
                EditorGUILayout.LabelField("📊 Service Statistics:", EditorStyles.boldLabel);
                EditorGUILayout.LabelField(bonusWordsService.GetStatistics(), EditorStyles.wordWrappedMiniLabel);
                EditorGUILayout.EndVertical();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("🧹 Clear All Caches", buttonSecondaryStyle, GUILayout.Height(28)))
                {
                    bonusWordsService.ClearAllCaches();
                }

                if (GUILayout.Button("🔍 Test Services", buttonSecondaryStyle, GUILayout.Height(28)))
                {
                    TestExternalServices();
                }
                EditorGUILayout.EndHorizontal();
            }
        }

        private void ApplyWordValidationSettings()
        {
            // Reinitialize the bonus words service with new settings
            bonusWordsService = new BonusWordsDetectionService(validationSettings);
            Debug.Log("🔄 Word validation settings applied. Bonus words service reinitialized.");
        }

        private async void TestExternalServices()
        {
            if (bonusWordsService == null)
            {
                Debug.LogWarning("⚠️ Bonus words service is not initialized!");
                return;
            }

            Debug.Log("🔍 Testing external services...");

            // Test with a simple Russian word
            string testWord = "тест";

            try
            {
                // Create temporary services for testing
                var services = new List<IWordValidationService>();

                if (validationSettings.UseYandexDictionary && !string.IsNullOrEmpty(validationSettings.YandexApiKey))
                {
                    var yandexService = new YandexDictionaryService(validationSettings.YandexApiKey, validationSettings.RequestTimeoutSeconds);
                    services.Add(yandexService);
                }

                if (validationSettings.UseOpenAI && !string.IsNullOrEmpty(validationSettings.OpenAIApiKey))
                {
                    var openAIService = new OpenAIWordService(validationSettings.OpenAIApiKey, validationSettings.RequestTimeoutSeconds);
                    services.Add(openAIService);
                }

                if (services.Count == 0)
                {
                    Debug.LogWarning("⚠️ No external services configured for testing!");
                    return;
                }

                foreach (var service in services)
                {
                    Debug.Log($"🔍 Testing {service.ServiceName}...");

                    // Test service availability
                    bool isAvailable = await service.IsServiceAvailableAsync();
                    if (isAvailable)
                    {
                        Debug.Log($"✅ {service.ServiceName} is available");

                        // Test word validation
                        bool isValid = await service.IsValidWordAsync(testWord);
                        Debug.Log($"🔤 Word '{testWord}' validation result: {(isValid ? "✅ Valid" : "❌ Invalid")}");
                    }
                    else
                    {
                        Debug.LogWarning($"❌ {service.ServiceName} is not available");
                    }
                }

                Debug.Log("🎉 External services testing completed!");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ Error testing external services: {ex.Message}");
            }
        }

        private void DrawHelpTab()
        {
            // Word Distribution Section
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("📊 Word Distribution", sectionHeaderStyle);

            var helpTextStyle = new GUIStyle(EditorStyles.wordWrappedLabel)
            {
                fontSize = 11,
                padding = new RectOffset(8, 8, 8, 8)
            };

            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField(
                "Word Distribution - система распределения слов по уровням:\n\n" +
                "• WordsData содержит все доступные слова, сгруппированные по количеству букв\n" +
                "• LevelsData определяет параметры уровней (размер матрицы, длина слов)\n" +
                "• WordDistributionSO - результат распределения конкретных слов для каждого уровня\n\n" +
                "Алгоритм:\n" +
                "1. Рассчитывает количество слов: (rows × cols) / lettersCount\n" +
                "2. Выбирает первые N слов нужной длины из словаря\n" +
                "3. Удаляет использованные слова, чтобы избежать повторов\n" +
                "4. Создает запись для уровня с выбранными словами",
                helpTextStyle
            );
            EditorGUILayout.EndVertical();
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(12);

            // Generation Process
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("🔧 Generation Process", sectionHeaderStyle);

            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField(
                "Процесс генерации уровней:\n\n" +
                "1. Create Word Distribution - распределяет слова по уровням\n" +
                "2. Generate All Levels - создает матрицы для всех уровней\n" +
                "3. Generate One Level - создает матрицу для конкретного уровня\n\n" +
                "Каждый уровень генерируется несколько раз (Matrices Per Level) для разнообразия.\n" +
                "Если генерация не удается за Max Attempts Per Matrix попыток, уровень помечается как проблемный.",
                helpTextStyle
            );
            EditorGUILayout.EndVertical();
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(12);

            // Bonus Words Section
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("🎁 Bonus Words Detection", sectionHeaderStyle);

            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField(
                "Система поиска бонусных слов (обновлена с поддержкой внешних сервисов):\n\n" +
                "• Бонусные слова - это слова, которые можно собрать на уровне, но которые не являются основными загаданными словами\n" +
                "• Например: основные слова 'АРБУЗ', 'КАРТА', 'ДВЕРЬ', 'ПИРОГ', а игрок нашел 'ПАРТА'\n" +
                "• Слова собираются ЗМЕЙКОЙ по 4 направлениям: ↑ ↓ ← → (БЕЗ диагоналей)\n" +
                "• Каждая буква может использоваться только один раз в пределах одного слова\n\n" +
                "🔍 Источники валидации слов:\n" +
                "• 📚 Локальная база - WordsData (слова от 3+ букв)\n" +
                "• 🔍 Yandex Dictionary API - онлайн словарь (слова от 2+ букв)\n" +
                "• 🤖 OpenAI API - ИИ валидация через ChatGPT (слова от 2+ букв)\n\n" +
                "⚙️ Настройка:\n" +
                "1. Перейдите на вкладку 'Settings' → 'Word Validation Services'\n" +
                "2. Включите нужные сервисы и введите API ключи\n" +
                "3. Настройте минимальную/максимальную длину слов\n" +
                "4. Нажмите 'Apply Settings' для применения\n\n" +
                "📋 Использование:\n" +
                "1. Перейдите на вкладку 'Generation' → секция 'Bonus Words Detection' для поиска\n" +
                "2. Перейдите на вкладку 'Levels' для просмотра всех уровней и визуализации\n" +
                "3. Выберите уровень для анализа\n" +
                "4. Нажмите 'Find Bonus Words' или 'Show [СЛОВО]' для визуализации пути",
                helpTextStyle
            );
            EditorGUILayout.EndVertical();
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(12);

            // Validation System
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("✓ Validation System", sectionHeaderStyle);

            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField(
                "Система валидации проверяет качество сгенерированных уровней:\n\n" +
                "• Matrix Integrity - проверяет, что все слова можно найти в матрице\n" +
                "• Bad Words - проверяет отсутствие нежелательных слов\n" +
                "• Word Interference - проверяет, что слова не пересекаются неправильно\n" +
                "• Single Line Collection - проверяет, что слова нельзя собрать в одну линию\n\n" +
                "При обнаружении проблем появляется кнопка регенерации проблемных уровней.",
                helpTextStyle
            );
            EditorGUILayout.EndVertical();
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(12);

            // BackwardsProbability
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("🔄 BackwardsProbability", sectionHeaderStyle);

            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField(
                "BackwardsProbability (0-100%) - вероятность размещения слов в обратном направлении:\n\n" +
                "• 0% - все слова только вправо (→) и вниз (↓)\n" +
                "• 50% - половина слов в обратном направлении влево (←) и вверх (↑)\n" +
                "• 100% - все слова только в обратном направлении\n\n" +
                "Это позволяет постепенно увеличивать сложность игры.",
                helpTextStyle
            );
            EditorGUILayout.EndVertical();
            EditorGUILayout.EndVertical();
        }

        private void ResetSettingsToDefaults()
        {
            maxAttemptsPerMatrix = 5000;
            generateCount = 3;
            fromWhatLevelRepeat = 40;
            fromStart = 38;
            toEnd = 49;
            preventStraightLines = true;
            bendPriority = 0.6f;
            fastGeneration = true;
            enableMatrixUniquenessCheck = true;
            enableBadWordCheck = true;
            enableMatrixIntegrityCheck = true;
            enableStraightLineCheck = true;
            strictFirstMatrix = true;
            badWordValidationScope = MatrixValidationScope.AllMatrices;
            matrixIntegrityValidationScope = MatrixValidationScope.AllMatrices;
            matrixUniquenessValidationScope = MatrixValidationScope.AllMatrices;
            straightLineValidationScope = MatrixValidationScope.FirstMatrixOnly;
            levelsPath = "Assets/_Project/Prefabs/SO/Levels";
            wordDistributionPath = "Assets/_Project/Prefabs/SO/WordDistribution/WordDistribution.asset";
            levelsDataPath = "Assets/_Project/Prefabs/SO/tables/LevelsData.asset";
            wordsDataPath = "Assets/_Project/Prefabs/SO/tables/WordsData.asset";
            levelsConfigPath = "Assets/_Project/Prefabs/SO/LevelsConfig.asset";
            badWordsPath = "Assets/_Project/Prefabs/SO/tables/BannedData.asset";
        }

        private void DrawValidationScopeSettings()
        {
            EditorGUI.indentLevel++;

            // Bad Word Validation Scope
            EditorGUILayout.LabelField("Bad Word Validation", EditorStyles.boldLabel);
            badWordValidationScope = (MatrixValidationScope)EditorGUILayout.EnumPopup("Scope", badWordValidationScope);
            EditorGUILayout.HelpBox(GetDescription(badWordValidationScope), MessageType.None);

            EditorGUILayout.Space(5);

            // Matrix Integrity Validation Scope
            EditorGUILayout.LabelField("Matrix Integrity Validation", EditorStyles.boldLabel);
            matrixIntegrityValidationScope = (MatrixValidationScope)EditorGUILayout.EnumPopup("Scope", matrixIntegrityValidationScope);
            EditorGUILayout.HelpBox(GetDescription(matrixIntegrityValidationScope), MessageType.None);

            EditorGUILayout.Space(5);

            // Matrix Uniqueness Validation Scope
            EditorGUILayout.LabelField("Matrix Uniqueness Validation", EditorStyles.boldLabel);
            matrixUniquenessValidationScope = (MatrixValidationScope)EditorGUILayout.EnumPopup("Scope", matrixUniquenessValidationScope);
            EditorGUILayout.HelpBox(GetDescription(matrixUniquenessValidationScope), MessageType.None);

            EditorGUILayout.Space(5);

            // Straight Line Validation Scope
            EditorGUILayout.LabelField("Straight Line Validation", EditorStyles.boldLabel);
            straightLineValidationScope = (MatrixValidationScope)EditorGUILayout.EnumPopup("Scope", straightLineValidationScope);
            EditorGUILayout.HelpBox(GetDescription(straightLineValidationScope), MessageType.None);

            EditorGUI.indentLevel--;
        }

        private string GetDescription(MatrixValidationScope scope)
        {
            return scope switch
            {
                MatrixValidationScope.FirstMatrixOnly => "Validates only the primary matrix (fastest, ensures main quality)",
                MatrixValidationScope.AllMatrices => "Validates all matrices in level (slowest, maximum quality)",
                MatrixValidationScope.FirstTwoMatrices => "Validates first two matrices (balanced approach)",
                MatrixValidationScope.LastMatrixOnly => "Validates only the last matrix (rarely used)",
                _ => "Unknown validation scope"
            };
        }

        private void UpdateGenerationSettings()
        {
            // Update generation conditions
            LevelGenerationService.EnableMatrixUniquenessCheck = enableMatrixUniquenessCheck;
            LevelGenerationService.EnableBadWordCheck = enableBadWordCheck;
            LevelGenerationService.EnableMatrixIntegrityCheck = enableMatrixIntegrityCheck;
            LevelGenerationService.EnableStraightLineCheck = enableStraightLineCheck;
            LevelGenerationService.StrictFirstMatrix = strictFirstMatrix;

            // Update validation scopes
            LevelGenerationService.BadWordValidationScope = (int)badWordValidationScope;
            LevelGenerationService.MatrixIntegrityValidationScope = (int)matrixIntegrityValidationScope;
            LevelGenerationService.MatrixUniquenessValidationScope = (int)matrixUniquenessValidationScope;
            LevelGenerationService.StraightLineValidationScope = (int)straightLineValidationScope;
        }

        private async UniTaskVoid FindBonusWordsAsync()
        {
            if (_bonusWordsLevel == null || bonusWordsService == null)
                return;

            _isSearchingBonusWords = true;
            _bonusWordsProgress = 0f;
            _foundBonusWords = null;

            try
            {
                var progress = new Progress<float>(p => _bonusWordsProgress = p);
                _foundBonusWords = await bonusWordsService.FindBonusWordsAsync(_bonusWordsLevel, progress);

                // Сохраняем найденные бонусные слова в уровень
                _bonusWordsLevel.BonusWords.Clear();
                foreach (var bonusWord in _foundBonusWords)
                {
                    _bonusWordsLevel.BonusWords.Add(new BonusWordData(bonusWord.Word, bonusWord.MatrixIndex));
                }

                // Помечаем уровень как измененный
                EditorUtility.SetDirty(_bonusWordsLevel);

                Debug.Log($"Found {_foundBonusWords.Count} bonus words for level {_bonusWordsLevel.LevelNum}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error finding bonus words: {ex.Message}");
            }
            finally
            {
                _isSearchingBonusWords = false;
                _bonusWordsProgress = 0f;
                Repaint();
            }
        }

        private void DrawBonusWordsResults()
        {
            EditorGUILayout.LabelField("Bonus Words Results", EditorStyles.boldLabel);

            if (_foundBonusWords.Count == 0)
            {
                EditorGUILayout.HelpBox("No bonus words found in this level.", MessageType.Info);
                return;
            }

            EditorGUILayout.HelpBox($"Found {_foundBonusWords.Count} bonus words", MessageType.Info);

            EditorGUILayout.Space(5);

            // Group by matrix
            var groupedByMatrix = _foundBonusWords.GroupBy(w => w.MatrixIndex).OrderBy(g => g.Key);

            foreach (var matrixGroup in groupedByMatrix)
            {
                EditorGUILayout.LabelField($"Matrix {matrixGroup.Key}:", EditorStyles.boldLabel);

                var wordsInMatrix = matrixGroup.OrderBy(w => w.Word).ToList();

                EditorGUI.indentLevel++;
                foreach (var bonusWord in wordsInMatrix)
                {
                    EditorGUILayout.LabelField($"• {bonusWord.Word} ({bonusWord.Length} letters)");
                }
                EditorGUI.indentLevel--;

                EditorGUILayout.Space(5);
            }

            // Summary by length
            EditorGUILayout.LabelField("Summary by Length:", EditorStyles.boldLabel);
            var lengthGroups = _foundBonusWords.GroupBy(w => w.Length).OrderBy(g => g.Key);

            EditorGUI.indentLevel++;
            foreach (var lengthGroup in lengthGroups)
            {
                EditorGUILayout.LabelField($"{lengthGroup.Key} letters: {lengthGroup.Count()} words");
            }
            EditorGUI.indentLevel--;
        }



        private void DrawLevelInformation()
        {
            EditorGUILayout.LabelField("Level Information", EditorStyles.boldLabel);

            EditorGUI.indentLevel++;

            // Basic Info
            EditorGUILayout.LabelField($"Level Number: {_selectedLevelForView.LevelNum}");
            EditorGUILayout.LabelField($"Matrix Size: {_selectedLevelForView.RowsCount} x {_selectedLevelForView.ColumnsCount}");
            EditorGUILayout.LabelField($"Total Matrices: {_selectedLevelForView.FillMatrixes?.Count ?? 0}");

            EditorGUILayout.Space(5);

            // Main Words
            EditorGUILayout.LabelField("Main Words:", EditorStyles.boldLabel);
            if (_selectedLevelForView.Words != null && _selectedLevelForView.Words.Count > 0)
            {
                foreach (var word in _selectedLevelForView.Words)
                {
                    EditorGUILayout.LabelField($"• {word} ({word.Length} letters)");
                }
            }
            else
            {
                EditorGUILayout.LabelField("No main words found");
            }

            EditorGUILayout.Space(5);

            // Bonus Words
            EditorGUILayout.LabelField("Bonus Words:", EditorStyles.boldLabel);
            if (_selectedLevelForView.BonusWords != null && _selectedLevelForView.BonusWords.Count > 0)
            {
                var groupedBonusWords = _selectedLevelForView.BonusWords.GroupBy(w => w.MatrixIndex).OrderBy(g => g.Key);
                foreach (var matrixGroup in groupedBonusWords)
                {
                    EditorGUILayout.LabelField($"Matrix {matrixGroup.Key}:");
                    EditorGUI.indentLevel++;
                    foreach (var bonusWord in matrixGroup.OrderBy(w => w.Word))
                    {
                        EditorGUILayout.LabelField($"• {bonusWord.Word} ({bonusWord.Length} letters)");
                    }
                    EditorGUI.indentLevel--;
                }
            }
            else
            {
                EditorGUILayout.LabelField("No bonus words found");
                EditorGUILayout.HelpBox("Use 'Generation' tab → 'Find Bonus Words for All Levels' to calculate bonus words.", MessageType.Info);
            }

            EditorGUI.indentLevel--;
        }

        private void DrawMatrixViewer()
        {
            if (_selectedLevelForView.FillMatrixes == null || _selectedLevelForView.FillMatrixes.Count == 0)
            {
                EditorGUILayout.HelpBox("No matrices found in this level.", MessageType.Warning);
                return;
            }

            EditorGUILayout.LabelField("Matrix Viewer", EditorStyles.boldLabel);

            // Matrix Selection
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Matrix:", GUILayout.Width(50));
            _selectedMatrixIndex = EditorGUILayout.IntSlider(_selectedMatrixIndex, 0, _selectedLevelForView.FillMatrixes.Count - 1);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(5);

            // Bonus Words for current matrix
            var bonusWordsForMatrix = _selectedLevelForView.BonusWords?.Where(w => w.MatrixIndex == _selectedMatrixIndex).ToList();
            if (bonusWordsForMatrix != null && bonusWordsForMatrix.Count > 0)
            {
                EditorGUILayout.LabelField($"Bonus Words in Matrix {_selectedMatrixIndex}:", EditorStyles.boldLabel);

                EditorGUILayout.BeginHorizontal();
                foreach (var bonusWord in bonusWordsForMatrix)
                {
                    if (GUILayout.Button($"Show {bonusWord.Word}", GUILayout.Height(25)))
                    {
                        ShowBonusWordPath(bonusWord.Word);
                    }
                }
                EditorGUILayout.EndHorizontal();

                if (_isShowingBonusWord)
                {
                    if (GUILayout.Button("Stop Highlighting", GUILayout.Height(25)))
                    {
                        StopHighlighting();
                    }
                }

                EditorGUILayout.Space(5);
            }

            // Matrix Display
            DrawMatrix(_selectedLevelForView.FillMatrixes[_selectedMatrixIndex]);
        }

        private void DrawMatrix(FillMatrix matrix)
        {
            // Matrix header
            EditorGUILayout.LabelField("🔤 Matrix Viewer", sectionHeaderStyle);

            if (_isShowingBonusWord && !string.IsNullOrEmpty(_currentHighlightedWord))
            {
                var pathInfoStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    padding = new RectOffset(12, 12, 8, 8),
                    margin = new RectOffset(0, 0, 8, 8)
                };

                EditorGUILayout.BeginVertical(pathInfoStyle);
                EditorGUILayout.LabelField($"🐍 Showing path for: {_currentHighlightedWord}", EditorStyles.boldLabel);
                EditorGUILayout.LabelField("Snake-like path (only ↑↓←→ directions)", EditorStyles.miniLabel);
                EditorGUILayout.EndVertical();
            }

            // Matrix container with modern styling
            var matrixContainerStyle = new GUIStyle()
            {
                padding = new RectOffset(12, 12, 12, 12),
                margin = new RectOffset(0, 0, 8, 8),
                normal = { background = EditorGUIUtility.isProSkin ?
                    MakeTexture(new Color(0.25f, 0.25f, 0.25f, 1f)) :
                    MakeTexture(new Color(0.95f, 0.95f, 0.95f, 1f)) }
            };

            EditorGUILayout.BeginVertical(matrixContainerStyle);
            _matrixScrollPosition = EditorGUILayout.BeginScrollView(_matrixScrollPosition, GUILayout.Height(400));

            var cellSize = 38f;
            var spacing = 3f;

            for (int r = 0; r < matrix.RowCount; r++)
            {
                EditorGUILayout.BeginHorizontal();
                for (int c = 0; c < matrix.ColCount; c++)
                {
                    var cellPos = new Vector2Int(r, c);
                    var highlightIndex = _highlightedCells.IndexOf(cellPos);
                    var isHighlighted = highlightIndex >= 0;

                    var originalColor = GUI.backgroundColor;
                    var originalContentColor = GUI.contentColor;

                    // Modern cell styling
                    var cellStyle = new GUIStyle(GUI.skin.button)
                    {
                        fontSize = isHighlighted ? 11 : 13,
                        fontStyle = isHighlighted ? FontStyle.Bold : FontStyle.Normal,
                        border = new RectOffset(1, 1, 1, 1),
                        alignment = TextAnchor.MiddleCenter
                    };

                    if (isHighlighted)
                    {
                        // Modern highlight colors
                        if (highlightIndex == 0)
                        {
                            GUI.backgroundColor = EditorGUIUtility.isProSkin ?
                                new Color(0.4f, 0.8f, 0.4f, 1f) : new Color(0.6f, 0.9f, 0.6f, 1f); // Start
                        }
                        else if (highlightIndex == _highlightedCells.Count - 1)
                        {
                            GUI.backgroundColor = EditorGUIUtility.isProSkin ?
                                new Color(0.8f, 0.4f, 0.4f, 1f) : new Color(0.9f, 0.6f, 0.6f, 1f); // End
                        }
                        else
                        {
                            GUI.backgroundColor = EditorGUIUtility.isProSkin ?
                                new Color(0.8f, 0.8f, 0.4f, 1f) : new Color(0.9f, 0.9f, 0.6f, 1f); // Path
                        }

                        GUI.contentColor = EditorGUIUtility.isProSkin ? Color.black : Color.black;
                    }
                    else
                    {
                        GUI.backgroundColor = EditorGUIUtility.isProSkin ?
                            new Color(0.35f, 0.35f, 0.35f, 1f) : new Color(0.9f, 0.9f, 0.9f, 1f);
                        GUI.contentColor = EditorGUIUtility.isProSkin ?
                            new Color(0.9f, 0.9f, 0.9f) : new Color(0.2f, 0.2f, 0.2f);
                    }

                    var letter = matrix[r, c].GetCharLetter().ToString();

                    // Add sequence number for highlighted cells
                    var buttonText = letter;
                    if (isHighlighted)
                    {
                        buttonText = $"{letter}\n{highlightIndex + 1}";
                    }

                    GUILayout.Button(buttonText, cellStyle, GUILayout.Width(cellSize), GUILayout.Height(cellSize));

                    GUI.backgroundColor = originalColor;
                    GUI.contentColor = originalContentColor;
                }
                EditorGUILayout.EndHorizontal();
                GUILayout.Space(spacing);
            }

            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();

            // Modern legend
            if (_isShowingBonusWord)
            {
                EditorGUILayout.Space(8);

                var legendStyle = new GUIStyle()
                {
                    padding = new RectOffset(12, 12, 8, 8),
                    margin = new RectOffset(0, 0, 0, 0),
                    normal = { background = EditorGUIUtility.isProSkin ?
                        MakeTexture(new Color(0.22f, 0.22f, 0.22f, 1f)) :
                        MakeTexture(new Color(0.97f, 0.97f, 0.97f, 1f)) }
                };

                EditorGUILayout.BeginVertical(legendStyle);
                EditorGUILayout.LabelField("🗺️ Path Legend:", EditorStyles.boldLabel);

                EditorGUILayout.BeginHorizontal();

                // Modern legend items
                var startColor = EditorGUIUtility.isProSkin ?
                    new Color(0.4f, 0.8f, 0.4f, 1f) : new Color(0.6f, 0.9f, 0.6f, 1f);
                var pathColor = EditorGUIUtility.isProSkin ?
                    new Color(0.8f, 0.8f, 0.4f, 1f) : new Color(0.9f, 0.9f, 0.6f, 1f);
                var endColor = EditorGUIUtility.isProSkin ?
                    new Color(0.8f, 0.4f, 0.4f, 1f) : new Color(0.9f, 0.6f, 0.6f, 1f);

                DrawLegendItem("🟢 Start", startColor);
                GUILayout.Space(8);
                DrawLegendItem("🟡 Path", pathColor);
                GUILayout.Space(8);
                DrawLegendItem("🔴 End", endColor);

                EditorGUILayout.EndHorizontal();
                EditorGUILayout.EndVertical();
            }
        }

        private Texture2D MakeTexture(Color color)
        {
            var texture = new Texture2D(1, 1);
            texture.SetPixel(0, 0, color);
            texture.Apply();
            return texture;
        }

        private void FindBonusWordsForSelectedLevel()
        {
            if (_selectedLevelForView == null || bonusWordsService == null)
                return;

            FindBonusWordsForLevelAsync(_selectedLevelForView).Forget();
        }

        private async UniTaskVoid FindBonusWordsForLevelAsync(LevelsSO level)
        {
            try
            {
                var foundWords = await bonusWordsService.FindBonusWordsAsync(level);

                level.BonusWords.Clear();
                foreach (var bonusWord in foundWords)
                {
                    level.BonusWords.Add(new BonusWordData(bonusWord.Word, bonusWord.MatrixIndex));
                }

                EditorUtility.SetDirty(level);
                Repaint();

                Debug.Log($"Found {foundWords.Count} bonus words for level {level.LevelNum}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error finding bonus words: {ex.Message}");
            }
        }

        private void ShowBonusWordPath(string word)
        {
            Debug.Log($"🎯 Attempting to show path for bonus word: '{word}'");

            // Проверяем, что слово действительно есть в списке бонусных слов для этой матрицы
            var bonusWordsForMatrix = _selectedLevelForView.BonusWords?.Where(w => w.MatrixIndex == _selectedMatrixIndex && w.Word == word).ToList();
            if (bonusWordsForMatrix == null || bonusWordsForMatrix.Count == 0)
            {
                Debug.LogError($"❌ Word '{word}' is not in bonus words list for matrix {_selectedMatrixIndex}!");
                return;
            }

            // Найти путь для этого слова в текущей матрице
            var matrix = _selectedLevelForView.FillMatrixes[_selectedMatrixIndex];
            var path = FindWordPath(matrix, word);

            if (path != null && path.Count > 0)
            {
                _highlightedCells = path;
                _currentHighlightedWord = word;
                _isShowingBonusWord = true;
                _currentBonusWordIndex = 0;
                _highlightTimer = 0f;

                Debug.Log($"🐍 Showing snake path for word '{word}': {string.Join(" → ", path.Select((p, i) => $"{matrix[p.x, p.y].GetCharLetter()}({i+1})"))}");

                Repaint();
            }
            else
            {
                Debug.LogWarning($"⚠️ Could not find snake path for word '{word}' in matrix {_selectedMatrixIndex}");
                Debug.LogWarning($"This suggests the bonus word detection algorithm found a word that cannot be collected as a snake!");

                // Предлагаем пересчитать бонусные слова
                if (EditorUtility.DisplayDialog("Path Not Found",
                    $"Could not find a valid snake path for '{word}'.\n\nThis might indicate an issue with bonus word detection.\n\nWould you like to recalculate bonus words for this level?",
                    "Recalculate", "Cancel"))
                {
                    FindBonusWordsForSelectedLevel();
                }
            }
        }

        private List<Vector2Int> FindWordPath(FillMatrix matrix, string word)
        {
            Debug.Log($"🔍 Searching for word '{word}' in matrix {_selectedMatrixIndex}...");

            // Показываем матрицу для отладки
            var matrixDebug = "";
            for (int r = 0; r < matrix.RowCount; r++)
            {
                for (int c = 0; c < matrix.ColCount; c++)
                {
                    matrixDebug += matrix[r, c].GetCharLetter() + " ";
                }
                matrixDebug += "\n";
            }
            Debug.Log($"Matrix content:\n{matrixDebug}");

            // Ищем все возможные начальные позиции
            var startPositions = new List<Vector2Int>();
            char firstChar = char.ToUpper(word[0]);

            for (int r = 0; r < matrix.RowCount; r++)
            {
                for (int c = 0; c < matrix.ColCount; c++)
                {
                    char matrixChar = char.ToUpper(matrix[r, c].GetCharLetter());
                    if (matrixChar == firstChar)
                    {
                        startPositions.Add(new Vector2Int(r, c));
                    }
                }
            }

            Debug.Log($"Found {startPositions.Count} potential start positions for '{firstChar}': {string.Join(", ", startPositions)}");

            // Пробуем найти путь от каждой начальной позиции
            foreach (var startPos in startPositions)
            {
                var path = new List<Vector2Int>();
                var visited = new bool[matrix.RowCount, matrix.ColCount];

                Debug.Log($"Trying to find path starting from ({startPos.x}, {startPos.y})...");

                if (FindWordPathRecursive(matrix, word, startPos.x, startPos.y, 0, path, visited))
                {
                    Debug.Log($"✅ Found path for '{word}': {string.Join(" → ", path.Select(p => $"({p.x},{p.y})"))}");
                    return path;
                }
            }

            Debug.LogWarning($"❌ No valid snake path found for word '{word}'");
            return null;
        }

        private bool FindWordPathRecursive(FillMatrix matrix, string word, int row, int col, int letterIndex,
            List<Vector2Int> path, bool[,] visited)
        {
            // Проверяем границы и посещенность
            if (row < 0 || row >= matrix.RowCount || col < 0 || col >= matrix.ColCount || visited[row, col])
                return false;

            // Проверяем соответствие буквы (приводим к верхнему регистру для сравнения)
            char matrixChar = char.ToUpper(matrix[row, col].GetCharLetter());
            char wordChar = char.ToUpper(word[letterIndex]);

            if (matrixChar != wordChar)
                return false;

            // Помечаем клетку как посещенную и добавляем в путь
            visited[row, col] = true;
            path.Add(new Vector2Int(row, col));

            // Если это последняя буква слова - успех!
            if (letterIndex == word.Length - 1)
                return true;

            // Проверяем только 4 направления (вверх, вниз, влево, вправо)
            var directions = new Vector2Int[]
            {
                new(-1, 0),  // Вверх
                new(1, 0),   // Вниз
                new(0, -1),  // Влево
                new(0, 1)    // Вправо
            };

            // Рекурсивно ищем следующую букву
            foreach (var dir in directions)
            {
                int newRow = row + dir.x;
                int newCol = col + dir.y;

                if (FindWordPathRecursive(matrix, word, newRow, newCol, letterIndex + 1, path, visited))
                    return true;
            }

            // Откат: снимаем пометку и удаляем из пути
            visited[row, col] = false;
            path.RemoveAt(path.Count - 1);
            return false;
        }

        private void UpdateHighlightAnimation()
        {
            if (!_isShowingBonusWord)
            {
                EditorApplication.update -= UpdateHighlightAnimation;
                return;
            }

            _highlightTimer += 0.016f; // ~60 FPS

            if (_highlightTimer >= HIGHLIGHT_DURATION)
            {
                _highlightTimer = 0f;
                _currentBonusWordIndex = (_currentBonusWordIndex + 1) % _highlightedCells.Count;
            }

            Repaint();
        }

        private void StopHighlighting()
        {
            _isShowingBonusWord = false;
            _highlightedCells.Clear();
            _currentHighlightedWord = "";
            EditorApplication.update -= UpdateHighlightAnimation;
            Repaint();
        }

        private void LoadAllLevels()
        {
            _allLevels.Clear();

            // Поиск всех LevelsSO файлов в проекте
            string[] guids = AssetDatabase.FindAssets("t:LevelsSO");

            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                LevelsSO level = AssetDatabase.LoadAssetAtPath<LevelsSO>(path);

                if (level != null)
                {
                    _allLevels.Add(level);
                }
            }

            // Сортируем по номеру уровня
            _allLevels.Sort((a, b) => a.LevelNum.CompareTo(b.LevelNum));

            Debug.Log($"Loaded {_allLevels.Count} levels");
        }



        private void DrawLevelsListContent()
        {
            // Header with count
            var headerStyle = new GUIStyle(EditorStyles.miniLabel)
            {
                fontStyle = FontStyle.Bold,
                normal = { textColor = EditorGUIUtility.isProSkin ?
                    new Color(0.8f, 0.8f, 0.8f) : new Color(0.3f, 0.3f, 0.3f) }
            };
            EditorGUILayout.LabelField($"📋 Levels ({_allLevels.Count})", headerStyle);

            if (_allLevels.Count == 0)
            {
                EditorGUILayout.Space(20);
                var emptyStyle = new GUIStyle(EditorStyles.centeredGreyMiniLabel)
                {
                    fontSize = 11,
                    normal = { textColor = EditorGUIUtility.isProSkin ?
                        new Color(0.6f, 0.6f, 0.6f) : new Color(0.5f, 0.5f, 0.5f) }
                };
                EditorGUILayout.LabelField("No levels loaded\nClick 'Load Levels' to refresh", emptyStyle);
                return;
            }

            // Filter levels
            var filteredLevels = _allLevels;
            if (!string.IsNullOrEmpty(_levelSearchFilter))
            {
                filteredLevels = _allLevels.Where(level =>
                    level.LevelNum.ToString().Contains(_levelSearchFilter) ||
                    level.name.ToLower().Contains(_levelSearchFilter.ToLower())
                ).ToList();
            }

            // Filter info
            if (filteredLevels.Count != _allLevels.Count)
            {
                var filterStyle = new GUIStyle(EditorStyles.miniLabel)
                {
                    normal = { textColor = EditorGUIUtility.isProSkin ?
                        new Color(0.7f, 0.7f, 0.7f) : new Color(0.4f, 0.4f, 0.4f) }
                };
                EditorGUILayout.LabelField($"Showing {filteredLevels.Count} of {_allLevels.Count}", filterStyle);
            }

            EditorGUILayout.Space(8);

            // Level list
            foreach (var level in filteredLevels)
            {
                var isSelected = _selectedLevelForView == level;

                // Modern list item style
                var itemStyle = new GUIStyle(GUI.skin.button)
                {
                    alignment = TextAnchor.MiddleLeft,
                    padding = new RectOffset(12, 12, 8, 8),
                    fontSize = 11,
                    fontStyle = FontStyle.Normal,
                    border = new RectOffset(0, 0, 0, 1),
                    margin = new RectOffset(0, 0, 0, 1)
                };

                if (isSelected)
                {
                    itemStyle.normal.background = EditorGUIUtility.isProSkin ?
                        MakeTexture(new Color(0.4f, 0.4f, 0.4f, 1f)) :
                        MakeTexture(new Color(0.85f, 0.85f, 0.85f, 1f));
                    itemStyle.normal.textColor = EditorGUIUtility.isProSkin ?
                        new Color(0.95f, 0.95f, 0.95f) : new Color(0.15f, 0.15f, 0.15f);
                }
                else
                {
                    itemStyle.normal.background = EditorGUIUtility.isProSkin ?
                        MakeTexture(new Color(0.26f, 0.26f, 0.26f, 1f)) :
                        MakeTexture(new Color(0.96f, 0.96f, 0.96f, 1f));
                    itemStyle.normal.textColor = EditorGUIUtility.isProSkin ?
                        new Color(0.8f, 0.8f, 0.8f) : new Color(0.3f, 0.3f, 0.3f);

                    itemStyle.hover.background = EditorGUIUtility.isProSkin ?
                        MakeTexture(new Color(0.32f, 0.32f, 0.32f, 1f)) :
                        MakeTexture(new Color(0.92f, 0.92f, 0.92f, 1f));
                }

                // Level info
                var levelText = $"Level {level.LevelNum}";
                var bonusCount = level.BonusWords?.Count ?? 0;
                var matrixCount = level.FillMatrixes?.Count ?? 0;

                if (bonusCount > 0)
                    levelText += $" • {bonusCount} bonus";
                if (matrixCount > 0)
                    levelText += $" • {matrixCount} matrices";

                if (GUILayout.Button(levelText, itemStyle, GUILayout.Height(28)))
                {
                    _selectedLevelForView = level;
                    _selectedMatrixIndex = 0;
                    StopHighlighting();
                }
            }
        }



        private void DrawLevelDetailsContent()
        {
            EditorGUILayout.LabelField($"Level {_selectedLevelForView.LevelNum} Details", EditorStyles.boldLabel);

            // Level Information
            DrawLevelInformation();

            EditorGUILayout.Space(10);

            // Matrix Viewer
            DrawMatrixViewer();
        }

        private async UniTaskVoid FindBonusWordsForAllLevelsAsync()
        {
            if (bonusWordsService == null)
            {
                Debug.LogError("❌ BonusWordsDetectionService is not initialized!");
                return;
            }

            // Загружаем все уровни если еще не загружены
            if (_allLevels.Count == 0)
            {
                LoadAllLevels();
            }

            if (_allLevels.Count == 0)
            {
                Debug.LogWarning("⚠️ No levels found in project!");
                return;
            }

            _isSearchingBonusWords = true;
            _bonusWordsProgress = 0f;
            _processedLevelsCount = 0;
            _totalLevelsCount = _allLevels.Count;
            _currentProcessingLevel = "";

            try
            {
                Debug.Log($"🔍 Starting bonus words search for {_allLevels.Count} levels...");

                int processedLevels = 0;
                int totalBonusWords = 0;
                var startTime = System.DateTime.Now;

                foreach (var level in _allLevels)
                {
                    try
                    {
                        _currentProcessingLevel = $"Level {level.LevelNum}";
                        _processedLevelsCount = processedLevels;

                        Debug.Log($"🎯 Processing Level {level.LevelNum} ({processedLevels + 1}/{_allLevels.Count})...");

                        var progress = new Progress<float>(p =>
                        {
                            float overallProgress = (processedLevels + p) / _allLevels.Count;
                            _bonusWordsProgress = overallProgress;
                            Repaint();
                        });

                        var foundWords = await bonusWordsService.FindBonusWordsAsync(level, progress);

                        // Сохраняем найденные бонусные слова
                        level.BonusWords.Clear();
                        foreach (var bonusWord in foundWords)
                        {
                            level.BonusWords.Add(new BonusWordData(bonusWord.Word, bonusWord.MatrixIndex));
                        }

                        // Помечаем уровень как измененный
                        EditorUtility.SetDirty(level);

                        totalBonusWords += foundWords.Count;

                        var elapsed = System.DateTime.Now - startTime;
                        var avgTimePerLevel = elapsed.TotalSeconds / (processedLevels + 1);
                        var estimatedTimeLeft = avgTimePerLevel * (_allLevels.Count - processedLevels - 1);

                        Debug.Log($"✅ Level {level.LevelNum}: Found {foundWords.Count} bonus words. " +
                                 $"ETA: {estimatedTimeLeft:F0}s");
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"❌ Error processing Level {level.LevelNum}: {ex.Message}");
                    }

                    processedLevels++;
                    _processedLevelsCount = processedLevels;
                    _bonusWordsProgress = (float)processedLevels / _allLevels.Count;
                    Repaint();

                    // Даем Unity больше времени для обновления интерфейса между уровнями
                    await UniTask.Delay(50); // 50ms пауза между уровнями
                    await UniTask.Yield();
                }

                // Сохраняем все изменения
                AssetDatabase.SaveAssets();

                // Очищаем кэш для экономии памяти
                bonusWordsService.ClearCache();

                Debug.Log($"🎉 Bonus words search completed!");
                Debug.Log($"📊 Processed {processedLevels} levels, found {totalBonusWords} total bonus words");

                EditorUtility.DisplayDialog("Bonus Words Search Complete",
                    $"Successfully processed {processedLevels} levels.\n\nFound {totalBonusWords} total bonus words.\n\nAll changes have been saved.",
                    "OK");
            }
            catch (Exception ex)
            {
                Debug.LogError($"❌ Error during mass bonus words search: {ex.Message}");
                EditorUtility.DisplayDialog("Error", $"An error occurred during bonus words search:\n\n{ex.Message}", "OK");
            }
            finally
            {
                _isSearchingBonusWords = false;
                _bonusWordsProgress = 0f;
                _currentProcessingLevel = "";
                _processedLevelsCount = 0;
                _totalLevelsCount = 0;
                Repaint();
            }
        }

        private void DrawLegendItem(string label, Color color)
        {
            var legendItemStyle = new GUIStyle(GUI.skin.box)
            {
                normal = { background = MakeTexture(color) },
                fontSize = 10,
                fontStyle = FontStyle.Bold,
                alignment = TextAnchor.MiddleCenter,
                padding = new RectOffset(8, 8, 4, 4)
            };

            GUILayout.Box(label, legendItemStyle, GUILayout.Width(60), GUILayout.Height(24));
        }

        private void DrawPathField(string label, ref string path, GUIStyle textStyle)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(label, GUILayout.Width(140));
            path = EditorGUILayout.TextField(path, textStyle);
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.Space(2);
        }

        private void DrawValidationToggle(string label, ref bool value, string warningText, bool isPositive = false)
        {
            // Use a clean toggle style without duplicating marks
            EditorGUILayout.BeginHorizontal();
            value = EditorGUILayout.Toggle(value, GUILayout.Width(20));
            EditorGUILayout.LabelField(label, GUILayout.ExpandWidth(true));
            EditorGUILayout.EndHorizontal();

            if ((!value && !isPositive) || (value && isPositive))
            {
                var warningStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    fontSize = 10,
                    padding = new RectOffset(20, 8, 4, 4),
                    margin = new RectOffset(0, 0, 2, 6)
                };

                var messageType = isPositive ? "✓" : "⚠";
                var textColor = isPositive ?
                    (EditorGUIUtility.isProSkin ? new Color(0.6f, 0.9f, 0.6f) : new Color(0.2f, 0.6f, 0.2f)) :
                    (EditorGUIUtility.isProSkin ? new Color(0.9f, 0.8f, 0.4f) : new Color(0.8f, 0.6f, 0.1f));

                EditorGUILayout.BeginVertical(warningStyle);
                var messageStyle = new GUIStyle(EditorStyles.wordWrappedMiniLabel)
                {
                    normal = { textColor = textColor }
                };
                EditorGUILayout.LabelField($"{messageType} {warningText}", messageStyle);
                EditorGUILayout.EndVertical();
            }
        }

        private void DrawWordsDataTab()
        {
            if (wordsData == null)
            {
                EditorGUILayout.BeginVertical(cardStyle);
                EditorGUILayout.LabelField("WordsData Management", sectionHeaderStyle);

                var errorStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    fontSize = 12,
                    padding = new RectOffset(12, 12, 8, 8),
                    normal = { textColor = Color.red }
                };

                EditorGUILayout.BeginVertical(errorStyle);
                EditorGUILayout.LabelField("❌ WordsData not found!");
                EditorGUILayout.LabelField("Expected path: Assets/_Project/Prefabs/SO/tables/WordsData.asset");
                EditorGUILayout.EndVertical();

                if (GUILayout.Button("🔄 Reload WordsData", buttonPrimaryStyle, GUILayout.Height(32)))
                {
                    LoadWordsData();
                }

                EditorGUILayout.EndVertical();
                return;
            }

            // Header with statistics
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("WordsData Management", sectionHeaderStyle);

            var statsStyle = new GUIStyle(EditorStyles.helpBox)
            {
                padding = new RectOffset(12, 12, 8, 8),
                margin = new RectOffset(0, 0, 8, 12)
            };

            EditorGUILayout.BeginVertical(statsStyle);
            int totalWords = 0;
            foreach (var wordSet in wordsData.WordsSets)
            {
                totalWords += wordSet.WordsArray.Length;
            }
            EditorGUILayout.LabelField($"📊 Total word sets: {wordsData.WordsSets.Count} | Total words: {totalWords}");
            EditorGUILayout.EndVertical();
            EditorGUILayout.EndVertical();

            // Search and validation controls
            EditorGUILayout.BeginVertical(cardStyle);
            EditorGUILayout.LabelField("Search & Validation", EditorStyles.boldLabel);

            // Search bar
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("🔍 Search:", GUILayout.Width(60));
            var searchStyle = new GUIStyle(EditorStyles.textField)
            {
                fontSize = 11,
                padding = new RectOffset(8, 8, 6, 6)
            };
            wordsSearchFilter = EditorGUILayout.TextField(wordsSearchFilter, searchStyle, GUILayout.Height(24));

            if (!string.IsNullOrEmpty(wordsSearchFilter) && GUILayout.Button("✕", GUILayout.Width(24), GUILayout.Height(24)))
            {
                wordsSearchFilter = "";
                GUI.FocusControl(null);
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(8);

            // Validation options
            EditorGUILayout.LabelField("Validation Options:", EditorStyles.boldLabel);
            enableEncodingValidation = EditorGUILayout.Toggle("✓ Encoding Validation", enableEncodingValidation);
            enableCorruptedDataValidation = EditorGUILayout.Toggle("✓ Corrupted Data Validation", enableCorruptedDataValidation);

            EditorGUILayout.Space(8);

            // Validation and save buttons
            EditorGUILayout.BeginHorizontal();

            GUI.enabled = !isValidating;
            if (GUILayout.Button(isValidating ? "🔄 Validating..." : "🔍 Validate All Words", buttonPrimaryStyle, GUILayout.Height(32)))
            {
                ValidateAllWords();
            }
            GUI.enabled = true;

            if (GUILayout.Button("💾 Save Changes", buttonSecondaryStyle, GUILayout.Height(32)))
            {
                SaveWordsData();
            }

            if (GUILayout.Button("🔄 Reload", buttonSecondaryStyle, GUILayout.Height(32)))
            {
                LoadWordsData();
                validationErrors.Clear();
            }

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();

            // Main content area
            EditorGUILayout.BeginHorizontal();

            // Left panel - Words list
            var leftPanelStyle = new GUIStyle(cardStyle)
            {
                padding = new RectOffset(12, 12, 12, 12)
            };
            EditorGUILayout.BeginVertical(leftPanelStyle, GUILayout.Width(400));
            DrawWordsListContent();
            EditorGUILayout.EndVertical();

            GUILayout.Space(12);

            // Right panel - Validation errors
            var rightPanelStyle = new GUIStyle(cardStyle)
            {
                padding = new RectOffset(16, 16, 16, 16)
            };
            EditorGUILayout.BeginVertical(rightPanelStyle);
            DrawValidationErrorsContent();
            EditorGUILayout.EndVertical();

            EditorGUILayout.EndHorizontal();
        }

        private void DrawWordsListContent()
        {
            EditorGUILayout.LabelField("All Words", EditorStyles.boldLabel);

            // Get all words with their info
            var allWords = new List<(string word, int setIndex, int wordIndex, int letterCount)>();

            for (int setIndex = 0; setIndex < wordsData.WordsSets.Count; setIndex++)
            {
                var wordSet = wordsData.WordsSets[setIndex];
                for (int wordIndex = 0; wordIndex < wordSet.WordsArray.Length; wordIndex++)
                {
                    var word = wordSet.WordsArray[wordIndex];

                    // Apply search filter
                    if (string.IsNullOrEmpty(wordsSearchFilter) ||
                        word.ToLower().Contains(wordsSearchFilter.ToLower()) ||
                        wordSet.LettersCount.ToString().Contains(wordsSearchFilter))
                    {
                        allWords.Add((word, setIndex, wordIndex, wordSet.LettersCount));
                    }
                }
            }

            // Sort by letter count, then alphabetically
            allWords.Sort((a, b) =>
            {
                int letterCompare = a.letterCount.CompareTo(b.letterCount);
                return letterCompare != 0 ? letterCompare : string.Compare(a.word, b.word);
            });

            EditorGUILayout.LabelField($"Found: {allWords.Count} words", EditorStyles.miniLabel);

            wordsDataScrollPosition = EditorGUILayout.BeginScrollView(wordsDataScrollPosition, GUILayout.ExpandHeight(true));

            foreach (var (word, setIndex, wordIndex, letterCount) in allWords)
            {
                EditorGUILayout.BeginHorizontal();

                // Word info
                var wordStyle = new GUIStyle(EditorStyles.label)
                {
                    fontSize = 11
                };
                EditorGUILayout.LabelField($"[{letterCount}]", GUILayout.Width(30));
                EditorGUILayout.LabelField(word, wordStyle, GUILayout.ExpandWidth(true));

                // Delete button
                if (GUILayout.Button("🗑️", GUILayout.Width(30)))
                {
                    if (EditorUtility.DisplayDialog("Delete Word",
                        $"Delete word '{word}' from {letterCount}-letter set?",
                        "Delete", "Cancel"))
                    {
                        RemoveWordFromSet(setIndex, wordIndex);
                        break; // Exit loop to avoid index issues
                    }
                }

                EditorGUILayout.EndHorizontal();
            }

            EditorGUILayout.EndScrollView();
        }

        private void DrawValidationErrorsContent()
        {
            EditorGUILayout.LabelField("Validation Results", EditorStyles.boldLabel);

            if (validationErrors.Count == 0)
            {
                var placeholderStyle = new GUIStyle(EditorStyles.centeredGreyMiniLabel)
                {
                    fontSize = 12,
                    normal = { textColor = EditorGUIUtility.isProSkin ? new Color(0.6f, 0.6f, 0.6f) : new Color(0.4f, 0.4f, 0.4f) }
                };

                GUILayout.FlexibleSpace();
                if (isValidating)
                {
                    EditorGUILayout.LabelField("🔄 Validating words...", placeholderStyle);
                }
                else
                {
                    EditorGUILayout.LabelField("No validation errors found.\nClick 'Validate All Words' to check for issues.", placeholderStyle);
                }
                GUILayout.FlexibleSpace();
                return;
            }

            // Error statistics
            var statsStyle = new GUIStyle(EditorStyles.helpBox)
            {
                padding = new RectOffset(8, 8, 6, 6),
                margin = new RectOffset(0, 0, 0, 8)
            };

            EditorGUILayout.BeginVertical(statsStyle);
            EditorGUILayout.LabelField($"❌ Found {validationErrors.Count} validation errors", EditorStyles.boldLabel);
            EditorGUILayout.EndVertical();

            // Scrollable errors list
            errorsScrollPosition = EditorGUILayout.BeginScrollView(errorsScrollPosition, GUILayout.ExpandHeight(true));

            for (int i = 0; i < validationErrors.Count; i++)
            {
                var error = validationErrors[i];

                var errorStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    padding = new RectOffset(8, 8, 6, 6),
                    margin = new RectOffset(0, 0, 2, 2),
                    normal = {
                        background = EditorGUIUtility.isProSkin ?
                            MakeTexture(new Color(0.6f, 0.2f, 0.2f, 0.3f)) :
                            MakeTexture(new Color(1f, 0.8f, 0.8f, 1f))
                    }
                };

                EditorGUILayout.BeginVertical(errorStyle);

                var errorTextStyle = new GUIStyle(EditorStyles.wordWrappedLabel)
                {
                    fontSize = 10,
                    normal = {
                        textColor = EditorGUIUtility.isProSkin ?
                            new Color(1f, 0.8f, 0.8f) :
                            new Color(0.6f, 0.1f, 0.1f)
                    }
                };

                EditorGUILayout.LabelField($"{i + 1}. {error}", errorTextStyle);
                EditorGUILayout.EndVertical();
            }

            EditorGUILayout.EndScrollView();

            // Action buttons
            EditorGUILayout.Space(8);
            EditorGUILayout.BeginHorizontal();

            // Delete all problematic words button
            if (wordsWithErrors.Count > 0)
            {
                var deleteAllStyle = new GUIStyle(buttonPrimaryStyle)
                {
                    normal = {
                        background = EditorGUIUtility.isProSkin ?
                            MakeTexture(new Color(0.8f, 0.3f, 0.3f, 1f)) :
                            MakeTexture(new Color(0.9f, 0.4f, 0.4f, 1f))
                    },
                    hover = {
                        background = EditorGUIUtility.isProSkin ?
                            MakeTexture(new Color(0.9f, 0.4f, 0.4f, 1f)) :
                            MakeTexture(new Color(1f, 0.5f, 0.5f, 1f))
                    }
                };

                if (GUILayout.Button($"🗑️ Delete All {wordsWithErrors.Count} Problem Words", deleteAllStyle, GUILayout.Height(32)))
                {
                    DeleteAllProblematicWords();
                }
            }

            GUILayout.FlexibleSpace();

            if (GUILayout.Button("🗑️ Clear Error List", buttonSecondaryStyle, GUILayout.Height(28)))
            {
                validationErrors.Clear();
                wordsWithErrors.Clear();
            }

            EditorGUILayout.EndHorizontal();
        }

        private void ValidateAllWords()
        {
            if (isValidating) return;

            isValidating = true;
            validationErrors.Clear();
            wordsWithErrors.Clear();

            try
            {
                int totalWords = 0;
                int errorCount = 0;

                for (int setIndex = 0; setIndex < wordsData.WordsSets.Count; setIndex++)
                {
                    var wordSet = wordsData.WordsSets[setIndex];

                    for (int wordIndex = 0; wordIndex < wordSet.WordsArray.Length; wordIndex++)
                    {
                        var word = wordSet.WordsArray[wordIndex];
                        totalWords++;

                        bool hasError = false;

                        // Encoding validation
                        if (enableEncodingValidation)
                        {
                            if (HasEncodingIssues(word))
                            {
                                validationErrors.Add($"Encoding issue in word '{word}' (Set: {wordSet.LettersCount} letters, Index: {wordIndex})");
                                hasError = true;
                            }
                        }

                        // Corrupted data validation
                        if (enableCorruptedDataValidation)
                        {
                            if (HasCorruptedData(word, wordSet.LettersCount))
                            {
                                validationErrors.Add($"Corrupted data in word '{word}' (Set: {wordSet.LettersCount} letters, Index: {wordIndex})");
                                hasError = true;
                            }
                        }

                        // Add to problematic words list if has any error
                        if (hasError)
                        {
                            wordsWithErrors.Add((setIndex, wordIndex, word));
                            errorCount++;
                        }
                    }
                }

                Debug.Log($"Validation completed: {totalWords} words checked, {errorCount} errors found.");
            }
            finally
            {
                isValidating = false;
            }
        }

        private bool HasEncodingIssues(string word)
        {
            if (string.IsNullOrEmpty(word)) return true;

            // Check for common encoding issues
            if (word.Contains("�")) return true; // Replacement character
            if (word.Contains("\r") || word.Contains("\n")) return true; // Line breaks
            if (word.Contains("\t")) return true; // Tabs

            // Check for invalid Unicode characters
            foreach (char c in word)
            {
                if (char.IsControl(c) && c != '\u0000') return true;
                if (char.IsSurrogate(c)) return true;
            }

            return false;
        }

        private bool HasCorruptedData(string word, int expectedLength)
        {
            if (string.IsNullOrEmpty(word)) return true;

            // Check if word length matches expected length
            if (word.Trim().Length != expectedLength) return true;

            // Check for duplicate characters that might indicate corruption
            var charCount = new Dictionary<char, int>();
            foreach (char c in word.ToLower())
            {
                charCount[c] = charCount.GetValueOrDefault(c, 0) + 1;
                // If more than half the word is the same character, it's likely corrupted
                if (charCount[c] > word.Length / 2 && word.Length > 2) return true;
            }

            // Check for non-letter characters (except common ones like apostrophes)
            foreach (char c in word)
            {
                if (!char.IsLetter(c) && c != '\'' && c != '-' && c != ' ') return true;
            }

            return false;
        }

        private void DeleteAllProblematicWords()
        {
            if (wordsWithErrors.Count == 0) return;

            string message = $"Are you sure you want to delete all {wordsWithErrors.Count} words with validation errors?\n\n";
            message += "This will permanently remove:\n";

            // Show first few problematic words as preview
            int previewCount = Math.Min(5, wordsWithErrors.Count);
            for (int i = 0; i < previewCount; i++)
            {
                var (setIndex, wordIndex, word) = wordsWithErrors[i];
                var wordSet = wordsData.WordsSets[setIndex];
                message += $"• '{word}' ({wordSet.LettersCount} letters)\n";
            }

            if (wordsWithErrors.Count > previewCount)
            {
                message += $"... and {wordsWithErrors.Count - previewCount} more words\n";
            }

            message += "\nThis action cannot be undone!";

            if (!EditorUtility.DisplayDialog("Delete All Problematic Words", message, "Delete All", "Cancel"))
            {
                return;
            }

            // Sort by setIndex and wordIndex in descending order to avoid index shifting issues
            var sortedWordsWithErrors = wordsWithErrors
                .OrderByDescending(x => x.setIndex)
                .ThenByDescending(x => x.wordIndex)
                .ToList();

            int deletedCount = 0;

            foreach (var (setIndex, wordIndex, word) in sortedWordsWithErrors)
            {
                // Verify the word still exists at this position (in case of concurrent modifications)
                if (setIndex < wordsData.WordsSets.Count)
                {
                    var wordSet = wordsData.WordsSets[setIndex];
                    if (wordIndex < wordSet.WordsArray.Length && wordSet.WordsArray[wordIndex] == word)
                    {
                        var wordsList = wordSet.WordsArray.ToList();
                        wordsList.RemoveAt(wordIndex);
                        wordSet.WordsArray = wordsList.ToArray();
                        deletedCount++;
                    }
                }
            }

            // Mark as dirty and clear validation results
            EditorUtility.SetDirty(wordsData);
            validationErrors.Clear();
            wordsWithErrors.Clear();

            Debug.Log($"Successfully deleted {deletedCount} problematic words from WordsData.");

            // Show completion message
            EditorUtility.DisplayDialog("Deletion Complete",
                $"Successfully deleted {deletedCount} problematic words.\n\nDon't forget to save your changes!",
                "OK");
        }

        private void RemoveWordFromSet(int setIndex, int wordIndex)
        {
            if (setIndex >= 0 && setIndex < wordsData.WordsSets.Count)
            {
                var wordSet = wordsData.WordsSets[setIndex];
                if (wordIndex >= 0 && wordIndex < wordSet.WordsArray.Length)
                {
                    var wordsList = wordSet.WordsArray.ToList();
                    string removedWord = wordsList[wordIndex];
                    wordsList.RemoveAt(wordIndex);
                    wordSet.WordsArray = wordsList.ToArray();
                    EditorUtility.SetDirty(wordsData);

                    Debug.Log($"Removed word '{removedWord}' from {wordSet.LettersCount}-letter set");

                    // Remove from validation errors if it was there
                    validationErrors.RemoveAll(error => error.Contains($"'{removedWord}'"));

                    // Remove from problematic words list and update indices
                    wordsWithErrors.RemoveAll(x => x.setIndex == setIndex && x.wordIndex == wordIndex && x.word == removedWord);

                    // Update indices for words that come after the removed word in the same set
                    for (int i = 0; i < wordsWithErrors.Count; i++)
                    {
                        var (wSetIndex, wWordIndex, wWord) = wordsWithErrors[i];
                        if (wSetIndex == setIndex && wWordIndex > wordIndex)
                        {
                            wordsWithErrors[i] = (wSetIndex, wWordIndex - 1, wWord);
                        }
                    }
                }
            }
        }

        private void SaveWordsData()
        {
            if (wordsData != null)
            {
                EditorUtility.SetDirty(wordsData);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                Debug.Log("WordsData saved successfully!");

                // Clear validation errors as data has changed
                validationErrors.Clear();
                wordsWithErrors.Clear();
            }
        }
    }
}

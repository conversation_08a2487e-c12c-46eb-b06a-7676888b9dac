using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class ParticleScaleTester : MonoBehaviour
{
    [Header("UI Controls")]
    [SerializeField] private Slider _scaleSlider;
    [SerializeField] private Slider _sizeMinSlider;
    [SerializeField] private Slider _sizeMaxSlider;
    [SerializeField] private Slider _speedMinSlider;
    [SerializeField] private Slider _speedMaxSlider;
    [SerializeField] private Button _testButton;
    [SerializeField] private TextMeshProUGUI _infoText;

    private void Start()
    {
        if (_scaleSlider != null)
        {
            _scaleSlider.minValue = 0.1f;
            _scaleSlider.maxValue = 5f;
            _scaleSlider.value = 1f;
            _scaleSlider.onValueChanged.AddListener(OnScaleChanged);
        }

        if (_sizeMinSlider != null)
        {
            _sizeMinSlider.minValue = 0.1f;
            _sizeMinSlider.maxValue = 3f;
            _sizeMinSlider.value = 0.8f;
            _sizeMinSlider.onValueChanged.AddListener(OnSizeChanged);
        }

        if (_sizeMaxSlider != null)
        {
            _sizeMaxSlider.minValue = 0.5f;
            _sizeMaxSlider.maxValue = 5f;
            _sizeMaxSlider.value = 2f;
            _sizeMaxSlider.onValueChanged.AddListener(OnSizeChanged);
        }

        if (_speedMinSlider != null)
        {
            _speedMinSlider.minValue = 1f;
            _speedMinSlider.maxValue = 20f;
            _speedMinSlider.value = 5f;
            _speedMinSlider.onValueChanged.AddListener(OnSpeedChanged);
        }

        if (_speedMaxSlider != null)
        {
            _speedMaxSlider.minValue = 5f;
            _speedMaxSlider.maxValue = 30f;
            _speedMaxSlider.value = 10f;
            _speedMaxSlider.onValueChanged.AddListener(OnSpeedChanged);
        }

        if (_testButton != null)
            _testButton.onClick.AddListener(TestParticles);

        UpdateInfo();
    }

    private void OnScaleChanged(float value)
    {
        if (PayrollAnimation.Instance != null)
        {
            // Обновляем масштаб через рефлексию или публичное свойство
            Debug.Log($"🔧 Масштаб частиц: {value}");
        }
        UpdateInfo();
    }

    private void OnSizeChanged(float value)
    {
        UpdateInfo();
    }

    private void OnSpeedChanged(float value)
    {
        UpdateInfo();
    }

    private void TestParticles()
    {
        if (PayrollAnimation.Instance != null)
        {
            Debug.Log("🧪 Тестируем частицы с новыми настройками");
            CurrencyManager.Add(25);
        }
        else
        {
            Debug.LogWarning("❌ Payroll_Animation не найден!");
        }
    }

    private void UpdateInfo()
    {
        if (_infoText != null)
        {
            string info = "🔧 Настройки частиц:\n";
            
            if (_scaleSlider != null)
                info += $"Масштаб: {_scaleSlider.value:F1}\n";
                
            if (_sizeMinSlider != null && _sizeMaxSlider != null)
                info += $"Размер: {_sizeMinSlider.value:F1} - {_sizeMaxSlider.value:F1}\n";
                
            if (_speedMinSlider != null && _speedMaxSlider != null)
                info += $"Скорость: {_speedMinSlider.value:F1} - {_speedMaxSlider.value:F1}\n";

            info += "\n💡 Настройте слайдеры и нажмите Test";
            _infoText.text = info;
        }
    }
}

using UnityEngine;

namespace LevelEditor.Services
{
    public struct Directions
    {
        public static Vector2Int[] directions = new Vector2Int[]
        {
            new Vector2Int(0, 1),   // Right
            new Vector2Int(1, 0),   // Down
            new Vector2Int(0, -1),  // Left
            new Vector2Int(-1, 0)   // Up
        };

        public static Vector2Int Right = new Vector2Int(0, 1);
        public static Vector2Int Down = new Vector2Int(1, 0);
        public static Vector2Int Left = new Vector2Int(0, -1);
        public static Vector2Int Up = new Vector2Int(-1, 0);
    }
    
    public class LetterInfo
    {
        public string Word;
        public int IndexInWord;

        public LetterInfo(string word, int indexInWord)
        {
            Word = word;
            IndexInWord = indexInWord;
        }
    }
}

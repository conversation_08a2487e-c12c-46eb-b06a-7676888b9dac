using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// Пример использования CurrencyManager
/// Демонстрирует основные операции с валютой
/// </summary>
public class CurrencyExample : MonoBehaviour
{
    [Header("UI Elements")]
    [SerializeField] private TextMeshProUGUI balanceText;
    [SerializeField] private Button addButton;
    [SerializeField] private Button spendButton;
    [SerializeField] private Button resetButton;
    
    [Header("Settings")]
    [SerializeField] private int addAmount = 10;
    [SerializeField] private int spendAmount = 5;

    private void Start()
    {
        // Подписываемся на события валюты
        CurrencyManager.OnBalanceChanged += OnBalanceChanged;
        CurrencyManager.OnCurrencyAdded += OnCurrencyAdded;
        CurrencyManager.OnCurrencySpent += OnCurrencySpent;
        CurrencyManager.OnInsufficientFunds += OnInsufficientFunds;
        
        // Настраиваем кнопки
        if (addButton != null)
            addButton.onClick.AddListener(() => CurrencyManager.Add(addAmount));
            
        if (spendButton != null)
            spendButton.onClick.AddListener(() => CurrencyManager.Spend(spendAmount));
            
        if (resetButton != null)
            resetButton.onClick.AddListener(() => CurrencyManager.ResetCurrency());
        
        // Обновляем UI
        UpdateBalanceDisplay();
    }

    private void OnDestroy()
    {
        // Отписываемся от событий
        CurrencyManager.OnBalanceChanged -= OnBalanceChanged;
        CurrencyManager.OnCurrencyAdded -= OnCurrencyAdded;
        CurrencyManager.OnCurrencySpent -= OnCurrencySpent;
        CurrencyManager.OnInsufficientFunds -= OnInsufficientFunds;
    }

    /// <summary>
    /// Обработчик изменения баланса
    /// </summary>
    private void OnBalanceChanged(int newBalance)
    {
        UpdateBalanceDisplay();
        Debug.Log($"💰 Balance updated: {newBalance}");
    }

    /// <summary>
    /// Обработчик добавления валюты
    /// </summary>
    private void OnCurrencyAdded(int amount)
    {
        Debug.Log($"✅ Currency added: +{amount}");
        // Здесь можно добавить анимацию или звук
    }

    /// <summary>
    /// Обработчик траты валюты
    /// </summary>
    private void OnCurrencySpent(int amount)
    {
        Debug.Log($"💸 Currency spent: -{amount}");
        // Здесь можно добавить анимацию или звук
    }

    /// <summary>
    /// Обработчик недостатка средств
    /// </summary>
    private void OnInsufficientFunds(int requiredAmount)
    {
        Debug.LogWarning($"❌ Not enough currency! Required: {requiredAmount}, Available: {CurrencyManager.Balance}");
        // Здесь можно показать сообщение игроку
    }

    /// <summary>
    /// Обновляет отображение баланса в UI
    /// </summary>
    private void UpdateBalanceDisplay()
    {
        if (balanceText != null)
        {
            balanceText.text = $"Balance: {CurrencyManager.Balance}";
        }
    }
 
  
}

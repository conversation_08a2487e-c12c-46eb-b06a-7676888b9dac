%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a980bed33a1e6964d9e431f3aff7ab6a, type: 3}
  m_Name: Level4
  m_EditorClassIdentifier: 
  levelNum: 4
  lettersCount: 3
  rowsCount: 3
  columnsCount: 3
  backwardsProbability: 60
  fillMatrixes:
  - rows:
    - row:
      - Letter: "\u0442"
        Word: "\u0448\u0443\u0442"
        IndexInWord: 2
      - Letter: "\u0447"
        Word: "\u043C\u044F\u0447"
        IndexInWord: 2
      - Letter: "\u044F"
        Word: "\u043C\u044F\u0447"
        IndexInWord: 1
    - row:
      - Letter: "\u0443"
        Word: "\u0448\u0443\u0442"
        IndexInWord: 1
      - Letter: "\u0444"
        Word: "\u043C\u0438\u0444"
        IndexInWord: 2
      - Letter: "\u043C"
        Word: "\u043C\u044F\u0447"
        IndexInWord: 0
    - row:
      - Letter: "\u0448"
        Word: "\u0448\u0443\u0442"
        IndexInWord: 0
      - Letter: "\u0438"
        Word: "\u043C\u0438\u0444"
        IndexInWord: 1
      - Letter: "\u043C"
        Word: "\u043C\u0438\u0444"
        IndexInWord: 0
  - rows:
    - row:
      - Letter: "\u0442"
        Word: "\u0448\u0443\u0442"
        IndexInWord: 2
      - Letter: "\u043C"
        Word: "\u043C\u044F\u0447"
        IndexInWord: 0
      - Letter: "\u043C"
        Word: "\u043C\u0438\u0444"
        IndexInWord: 0
    - row:
      - Letter: "\u0443"
        Word: "\u0448\u0443\u0442"
        IndexInWord: 1
      - Letter: "\u044F"
        Word: "\u043C\u044F\u0447"
        IndexInWord: 1
      - Letter: "\u0438"
        Word: "\u043C\u0438\u0444"
        IndexInWord: 1
    - row:
      - Letter: "\u0448"
        Word: "\u0448\u0443\u0442"
        IndexInWord: 0
      - Letter: "\u0447"
        Word: "\u043C\u044F\u0447"
        IndexInWord: 2
      - Letter: "\u0444"
        Word: "\u043C\u0438\u0444"
        IndexInWord: 2
  words:
  - "\u043C\u044F\u0447"
  - "\u0448\u0443\u0442"
  - "\u043C\u0438\u0444"
  bonusWords: []

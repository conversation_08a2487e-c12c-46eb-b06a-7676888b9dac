using UnityEngine;
using UnityEngine.UI;
using VG;

public class CurrencyCheat : MonoBehaviour
{
    [SerializeField] private Button _cheatButton;
    [SerializeField] private int _cheatAmount = 9999;
    
    private int _originalBalance;
    private bool _cheatActive = false;

    private void Start()
    {
        if (_cheatButton != null)
        {
            _cheatButton.onClick.AddListener(ToggleCheat);
        }
    }

    private void OnDestroy()
    {
        if (_cheatButton != null)
        {
            _cheatButton.onClick.RemoveListener(ToggleCheat);
        }
    }

    public void ToggleCheat()
    {
        if (_cheatActive)
        {
            DisableCheat();
        }
        else
        {
            EnableCheat();
        }
    }

    private void EnableCheat()
    {
        _originalBalance = CurrencyManager.Balance;
        SetCurrencyWithoutSave(_cheatAmount);
        _cheatActive = true;
    }

    private void DisableCheat()
    {
        SetCurrencyWithoutSave(_originalBalance);
        _cheatActive = false;
    }

    private void SetCurrencyWithoutSave(int amount)
    {
        if (amount < 0) return;
        
        Saves.Int[Key_Save.Currency].Value = amount;
    }

    public bool IsCheatActive => _cheatActive;
    
    public void SetCheatAmount(int amount)
    {
        _cheatAmount = amount;
    }
}

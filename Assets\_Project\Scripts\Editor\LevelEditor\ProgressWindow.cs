using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using LevelEditor.Services;

namespace LevelEditor
{
    public class ProgressWindow : EditorWindow
    {
        private string title;
        private string currentOperation;
        private float progress;
        private List<string> logMessages = new List<string>();
        private Vector2 scrollPosition;
        private bool isCompleted;
        private Action onClose;
        
        public static ProgressWindow ShowProgress(string title, Action onClose = null)
        {
            var window = GetWindow<ProgressWindow>(true, title, true);
            window.title = title;
            window.onClose = onClose;
            window.isCompleted = false;
            window.logMessages.Clear();
            window.currentOperation = "Initializing...";
            window.progress = 0f;
            
            window.minSize = new Vector2(400, 300);
            window.maxSize = new Vector2(600, 500);
            window.position = new Rect(
                (Screen.currentResolution.width - 500) / 2,
                (Screen.currentResolution.height - 400) / 2,
                500, 400
            );
            
            window.Show();
            return window;
        }
        
        public void UpdateProgress(GenerationProgress progress)
        {
            this.currentOperation = progress.CurrentOperation;
            this.progress = progress.Progress;
            
            string message = $"[{DateTime.Now:HH:mm:ss}] {progress.CurrentOperation}";
            if (progress.CurrentLevelNumber > 0)
            {
                message += $" (Level {progress.CurrentLevelNumber})";
            }
            
            AddLogMessage(message);
            Repaint();
        }
        
        public void UpdateProgress(ValidationProgress progress)
        {
            this.currentOperation = $"{progress.ServiceName}: {progress.CurrentOperation}";
            this.progress = progress.Progress;
            
            string message = $"[{DateTime.Now:HH:mm:ss}] {progress.ServiceName}: {progress.CurrentOperation}";
            AddLogMessage(message);
            Repaint();
        }
        
        public void UpdateProgress(string operation, float progressValue)
        {
            this.currentOperation = operation;
            this.progress = progressValue;
            
            AddLogMessage($"[{DateTime.Now:HH:mm:ss}] {operation}");
            Repaint();
        }
        
        public void AddLogMessage(string message)
        {
            logMessages.Add(message);
            
            // Keep only last 100 messages to prevent memory issues
            if (logMessages.Count > 100)
            {
                logMessages.RemoveAt(0);
            }
        }
        
        public void SetCompleted(bool success, string message = "")
        {
            isCompleted = true;
            progress = 1f;
            currentOperation = success ? "Completed successfully" : "Completed with errors";
            
            if (!string.IsNullOrEmpty(message))
            {
                AddLogMessage($"[{DateTime.Now:HH:mm:ss}] {message}");
            }
            
            Repaint();
        }
        
        private void OnGUI()
        {
            EditorGUILayout.Space(10);
            
            // Title
            var titleStyle = new GUIStyle(EditorStyles.largeLabel)
            {
                fontSize = 16,
                fontStyle = FontStyle.Bold,
                alignment = TextAnchor.MiddleCenter
            };
            EditorGUILayout.LabelField(title, titleStyle);
            
            EditorGUILayout.Space(10);
            
            // Current operation
            EditorGUILayout.LabelField("Current Operation:", EditorStyles.boldLabel);
            EditorGUILayout.LabelField(currentOperation, EditorStyles.wordWrappedLabel);
            
            EditorGUILayout.Space(5);
            
            // Progress bar
            EditorGUILayout.LabelField("Progress:", EditorStyles.boldLabel);
            var rect = EditorGUILayout.GetControlRect(false, 20);
            EditorGUI.ProgressBar(rect, progress, $"{(progress * 100):F1}%");
            
            EditorGUILayout.Space(10);
            
            // Log messages
            EditorGUILayout.LabelField("Log:", EditorStyles.boldLabel);
            
            var logStyle = new GUIStyle(EditorStyles.textArea)
            {
                wordWrap = false,
                fontSize = 10
            };
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.ExpandHeight(true));
            
            foreach (var message in logMessages)
            {
                EditorGUILayout.SelectableLabel(message, logStyle, GUILayout.Height(16));
            }
            
            // Auto-scroll to bottom
            if (Event.current.type == EventType.Repaint && logMessages.Count > 0)
            {
                scrollPosition.y = Mathf.Infinity;
            }
            
            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.Space(10);
            
            // Close button
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();
            
            EditorGUI.BeginDisabledGroup(!isCompleted);
            if (GUILayout.Button("Close", GUILayout.Width(80), GUILayout.Height(25)))
            {
                Close();
            }
            EditorGUI.EndDisabledGroup();
            
            if (!isCompleted && GUILayout.Button("Cancel", GUILayout.Width(80), GUILayout.Height(25)))
            {
                if (EditorUtility.DisplayDialog("Cancel Operation", "Are you sure you want to cancel the current operation?", "Yes", "No"))
                {
                    Close();
                }
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space(5);
        }
        
        private void OnDestroy()
        {
            onClose?.Invoke();
        }
    }
}

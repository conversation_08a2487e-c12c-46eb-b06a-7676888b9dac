using System;
using System.Collections.Generic;
using UnityEngine;
using Cysharp.Threading.Tasks;

namespace LevelEditor.Services
{
    public enum MatrixValidationScope
    {
        FirstMatrixOnly = 0,    // Только первая матрица (индекс 0)
        AllMatrices = 1,        // Все матрицы в уровне
        FirstTwoMatrices = 2,   // Первые две матрицы (индексы 0, 1)
        LastMatrixOnly = 3      // Только последняя матрица
    }

    public class SingleLineCollectionValidationService : BaseValidationService
    {
        public override string ServiceName => "Single Line Collection Validation";
        
        protected override async UniTask ValidateInternalAsync(LevelsSO level, ValidationResult result, IProgress<ValidationProgress> progress)
        {
            if (level.FillMatrixes == null || level.FillMatrixes.Count == 0)
            {
                result.IsValid = false;
                result.Errors.Add(new ValidationError("Data", "Level has no matrices to validate"));
                return;
            }

            // Get validation scope from settings
            var scope = (MatrixValidationScope)LevelGenerationService.StraightLineValidationScope;
            var matricesToValidate = GetMatricesToValidate(level.FillMatrixes.Count, scope);

            ReportProgress(progress, $"Starting single line collection validation ({scope})", 0, matricesToValidate.Count);

            for (int i = 0; i < matricesToValidate.Count; i++)
            {
                int matrixIndex = matricesToValidate[i];
                var matrix = level.FillMatrixes[matrixIndex];

                ReportProgress(progress, $"Checking matrix {matrixIndex + 1}", i, matricesToValidate.Count);

                foreach (string word in level.Words)
                {
                    if (CanWordBeCollectedInStraightLine(matrix, word))
                    {
                        result.IsValid = false;
                        result.Errors.Add(new ValidationError(
                            "StraightLine",
                            $"Word '{word}' can be collected in a straight line in matrix {matrixIndex}",
                            matrixIndex,
                            $"Matrix should have snake-like patterns to ensure quality gameplay"
                        ));
                    }
                }

                await DelayForUI();
            }

            ReportProgress(progress, "Single line collection validation completed", matricesToValidate.Count, matricesToValidate.Count);
        }
        
        private bool CanWordBeCollectedInStraightLine(FillMatrix matrix, string word)
        {
            // Check all possible starting positions
            for (int r = 0; r < matrix.RowCount; r++)
            {
                for (int c = 0; c < matrix.ColCount; c++)
                {
                    if (matrix[r, c].GetCharLetter() == word[0])
                    {
                        // Check all 4 straight directions
                        var directions = new Vector2Int[]
                        {
                            new Vector2Int(0, 1),   // Right
                            new Vector2Int(1, 0),   // Down
                            new Vector2Int(0, -1),  // Left
                            new Vector2Int(-1, 0)   // Up
                        };
                        
                        foreach (var direction in directions)
                        {
                            if (CheckWordInStraightDirection(matrix, word, r, c, direction))
                            {
                                return true; // Word can be collected in straight line
                            }
                        }
                    }
                }
            }
            
            return false; // Word cannot be collected in any straight line
        }
        
        private bool CheckWordInStraightDirection(FillMatrix matrix, string word, int startR, int startC, Vector2Int direction)
        {
            int r = startR;
            int c = startC;
            
            for (int i = 0; i < word.Length; i++)
            {
                // Check bounds
                if (r < 0 || r >= matrix.RowCount || c < 0 || c >= matrix.ColCount)
                    return false;
                
                // Check if letter matches
                if (matrix[r, c].GetCharLetter() != word[i])
                    return false;
                
                // Move to next position
                r += direction.x;
                c += direction.y;
            }
            
            return true; // Word found in straight line
        }
        
        protected override string CreateSummary(ValidationResult result)
        {
            var scope = (MatrixValidationScope)LevelGenerationService.StraightLineValidationScope;
            var scopeName = GetScopeName(scope);

            if (result.IsValid)
            {
                return $"{ServiceName}: {scopeName} has proper snake-like patterns";
            }
            else
            {
                var straightLineErrors = result.Errors.FindAll(e => e.ErrorType == "StraightLine");
                return $"{ServiceName}: Found {straightLineErrors.Count} word(s) in {scopeName} that can be collected in straight lines";
            }
        }

        private List<int> GetMatricesToValidate(int totalMatrices, MatrixValidationScope scope)
        {
            var result = new List<int>();

            for (int i = 0; i < totalMatrices; i++)
            {
                if (ShouldValidateMatrix(scope, i, totalMatrices))
                {
                    result.Add(i);
                }
            }

            return result;
        }

        private bool ShouldValidateMatrix(MatrixValidationScope scope, int matrixIndex, int totalMatrices)
        {
            return scope switch
            {
                MatrixValidationScope.FirstMatrixOnly => matrixIndex == 0,
                MatrixValidationScope.AllMatrices => true,
                MatrixValidationScope.FirstTwoMatrices => matrixIndex <= 1,
                MatrixValidationScope.LastMatrixOnly => matrixIndex == totalMatrices - 1,
                _ => false
            };
        }

        private string GetScopeName(MatrixValidationScope scope)
        {
            return scope switch
            {
                MatrixValidationScope.FirstMatrixOnly => "First matrix",
                MatrixValidationScope.AllMatrices => "All matrices",
                MatrixValidationScope.FirstTwoMatrices => "First two matrices",
                MatrixValidationScope.LastMatrixOnly => "Last matrix",
                _ => "Selected matrices"
            };
        }
    }
}

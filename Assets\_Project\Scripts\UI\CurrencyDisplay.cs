using UnityEngine;
using TMPro;
using UnityEngine.UI;

public class CurrencyDisplay : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private TextMeshProUGUI _currencyText;
    [SerializeField] private Image _currencyIcon;

    [Header("Display Settings")]
    [SerializeField] private string _prefix = "";
    [SerializeField] private string _suffix = "";
    [SerializeField] private string _format = "N0";

    [Header("Animation Settings")]
    [SerializeField] private bool _animateChanges = true;
    [SerializeField] private float _animationDuration = 0.3f;
    [SerializeField] private AnimationCurve _animationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

    private int _displayedBalance = 0;
    private Coroutine _animationCoroutine;

    private void Start()
    {
        CurrencyManager.OnBalanceChanged += OnBalanceChanged;
        _displayedBalance = CurrencyManager.Balance;
        UpdateDisplay(_displayedBalance);
    }

    private void OnDestroy()
    {
        CurrencyManager.OnBalanceChanged -= OnBalanceChanged;
        if (_animationCoroutine != null)
            StopCoroutine(_animationCoroutine);
    }

    private void OnBalanceChanged(int newBalance)
    {
        if (_animateChanges && gameObject.activeInHierarchy)
            AnimateToBalance(newBalance);
        else
        {
            _displayedBalance = newBalance;
            UpdateDisplay(_displayedBalance);
        }
    }

    private void AnimateToBalance(int targetBalance)
    {
        if (_animationCoroutine != null)
            StopCoroutine(_animationCoroutine);

        _animationCoroutine = StartCoroutine(AnimateBalanceCoroutine(targetBalance));
    }

    private System.Collections.IEnumerator AnimateBalanceCoroutine(int targetBalance)
    {
        int startBalance = _displayedBalance;
        float elapsedTime = 0f;

        while (elapsedTime < _animationDuration)
        {
            elapsedTime += Time.unscaledDeltaTime;
            float progress = elapsedTime / _animationDuration;
            float curveValue = _animationCurve.Evaluate(progress);

            _displayedBalance = Mathf.RoundToInt(Mathf.Lerp(startBalance, targetBalance, curveValue));
            UpdateDisplay(_displayedBalance);

            yield return null;
        }

        _displayedBalance = targetBalance;
        UpdateDisplay(_displayedBalance);
        _animationCoroutine = null;
    }

    private void UpdateDisplay(int balance)
    {
        if (_currencyText != null)
        {
            string formattedBalance = balance.ToString(_format);
            _currencyText.text = $"{_prefix}{formattedBalance}{_suffix}";
        }
    }

    public void SetPrefix(string newPrefix)
    {
        _prefix = newPrefix;
        UpdateDisplay(_displayedBalance);
    }

    public void SetSuffix(string newSuffix)
    {
        _suffix = newSuffix;
        UpdateDisplay(_displayedBalance);
    }

    public void SetFormat(string newFormat)
    {
        _format = newFormat;
        UpdateDisplay(_displayedBalance);
    }

    public void SetAnimationEnabled(bool enabled) => _animateChanges = enabled;

    [ContextMenu("Force Update Display")]
    public void ForceUpdateDisplay()
    {
        _displayedBalance = CurrencyManager.Balance;
        UpdateDisplay(_displayedBalance);
    }

    public void SkipAnimation()
    {
        if (_animationCoroutine != null)
        {
            StopCoroutine(_animationCoroutine);
            _animationCoroutine = null;
        }

        _displayedBalance = CurrencyManager.Balance;
        UpdateDisplay(_displayedBalance);
    }
}

using UnityEngine;
using UnityEditor;
using UnityEngine.Networking;
using Cysharp.Threading.Tasks;

namespace LevelEditor
{
    /// <summary>
    /// Простой тестер для проверки сырых ответов от Yandex Dictionary API
    /// </summary>
    public class SimpleYandexTester : EditorWindow
    {
        private string _apiKey = "";
        private string _testWord = "УМ";
        private string _lastResponse = "";
        private Vector2 _scrollPosition;
        private bool _isTesting = false;

        [MenuItem("Tools/Level Editor/Simple Yandex Tester")]
        public static void ShowWindow()
        {
            var window = GetWindow<SimpleYandexTester>("Simple Yandex Tester");
            window.maxSize = new Vector2(800, 600);
            window.minSize = new Vector2(400, 300);
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("Simple Yandex Dictionary API Tester", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // API Key input
            EditorGUILayout.LabelField("API Key:");
            _apiKey = EditorGUILayout.PasswordField(_apiKey);
            EditorGUILayout.Space();

            // Test word input
            EditorGUILayout.LabelField("Test Word:");
            _testWord = EditorGUILayout.TextField(_testWord);
            EditorGUILayout.Space();

            // Test button
            GUI.enabled = !string.IsNullOrEmpty(_apiKey) && !string.IsNullOrEmpty(_testWord) && !_isTesting;
            if (GUILayout.Button("Test API Call"))
            {
                TestApiCall();
            }
            GUI.enabled = true;

            if (GUILayout.Button("Clear Response"))
            {
                _lastResponse = "";
            }

            EditorGUILayout.Space();

            // Status
            if (_isTesting)
            {
                EditorGUILayout.LabelField("Testing...", EditorStyles.helpBox);
            }

            // Response
            EditorGUILayout.LabelField("Raw API Response:", EditorStyles.boldLabel);
            _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);
            
            EditorGUILayout.TextArea(_lastResponse, GUILayout.ExpandHeight(true));
            
            EditorGUILayout.EndScrollView();
        }

        private async void TestApiCall()
        {
            _isTesting = true;
            _lastResponse = $"Testing word: '{_testWord}'\n\n";
            
            try
            {
                string url = $"https://dictionary.yandex.net/api/v1/dicservice.json/lookup?key={_apiKey}&lang=ru-ru&text={_testWord}";
                
                using (UnityWebRequest request = UnityWebRequest.Get(url))
                {
                    request.timeout = 10;
                    await request.SendWebRequest().ToUniTask();
                    
                    if (request.result == UnityWebRequest.Result.Success)
                    {
                        _lastResponse += "✅ API call successful!\n\n";
                        _lastResponse += "Raw JSON Response:\n";
                        _lastResponse += request.downloadHandler.text;
                        
                        // Попробуем распарсить ответ
                        _lastResponse += "\n\n=== PARSING ANALYSIS ===\n";
                        AnalyzeResponse(request.downloadHandler.text);
                    }
                    else
                    {
                        _lastResponse += $"❌ API call failed: {request.error}\n";
                        _lastResponse += $"Response Code: {request.responseCode}\n";
                        _lastResponse += $"Response: {request.downloadHandler.text}";
                    }
                }
            }
            catch (System.Exception ex)
            {
                _lastResponse += $"❌ Exception: {ex.Message}\n";
                _lastResponse += $"Stack trace: {ex.StackTrace}";
            }
            finally
            {
                _isTesting = false;
                Repaint();
            }
        }

        private void AnalyzeResponse(string jsonResponse)
        {
            _lastResponse += $"Response length: {jsonResponse.Length} characters\n";
            
            // Проверяем наличие основных полей
            if (jsonResponse.Contains("\"def\":["))
            {
                _lastResponse += "✅ Contains 'def' array\n";
                
                if (jsonResponse.Contains("\"def\":[]"))
                {
                    _lastResponse += "❌ 'def' array is empty\n";
                }
                else
                {
                    _lastResponse += "✅ 'def' array has content\n";
                    
                    // Ищем массив определений
                    int defStart = jsonResponse.IndexOf("\"def\":[");
                    if (defStart != -1)
                    {
                        int defEnd = jsonResponse.IndexOf("]", defStart);
                        if (defEnd != -1)
                        {
                            string defSection = jsonResponse.Substring(defStart + 7, defEnd - defStart - 7);
                            _lastResponse += $"Definitions section: {defSection}\n";
                            
                            // Ищем части речи
                            if (defSection.Contains("\"pos\":"))
                            {
                                _lastResponse += "✅ Contains 'pos' field\n";
                                
                                // Извлекаем все части речи
                                int posIndex = 0;
                                while ((posIndex = defSection.IndexOf("\"pos\":\"", posIndex)) != -1)
                                {
                                    posIndex += 7;
                                    int posEnd = defSection.IndexOf("\"", posIndex);
                                    if (posEnd != -1)
                                    {
                                        string pos = defSection.Substring(posIndex, posEnd - posIndex);
                                        _lastResponse += $"Found POS: '{pos}'\n";
                                        posIndex = posEnd;
                                    }
                                    else
                                    {
                                        break;
                                    }
                                }
                            }
                            else
                            {
                                _lastResponse += "❌ No 'pos' field found\n";
                            }
                        }
                    }
                }
            }
            else
            {
                _lastResponse += "❌ No 'def' array found\n";
            }
        }
    }
}

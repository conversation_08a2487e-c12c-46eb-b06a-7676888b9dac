using TMPro;
using UnityEngine;

public class LevelIndexView : MonoBehaviour
{
    [SerializeField] private TMP_Text _text;

    private void Awake() =>
        LevelManager.onStart += UpdateLevelIndexText;

    private void OnDestroy() =>
        LevelManager.onStart -= UpdateLevelIndexText;

    private void UpdateLevelIndexText()
    {
        int index = LevelManager.Instance.HasTutorialCompleted == true ? LevelManager.CurrentLevel + 2 : 1;
        string text = $"Ур. {index}";
        _text.text = text;
        _text.ForceMeshUpdate();
    }
}

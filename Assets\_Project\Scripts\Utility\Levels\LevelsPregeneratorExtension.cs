using UnityEngine;
using System.Collections.Generic;

public static class LevelsPregeneratorExtension
{
    public static bool ValidateMatrixForBadWords(char[,] matrix, List<string> bannedWords)
    {
        return !BadWordValidationService.HasBadWords(matrix, bannedWords);
    }

    public static bool ValidateMatrixForBadWords(FillMatrix matrix, List<string> bannedWords)
    {
        return !BadWordValidationService.HasBadWords(matrix, bannedWords);
    }

    public static List<BadWordMatch> FindBadWordsInMatrix(char[,] matrix, List<string> bannedWords)
    {
        return BadWordValidationService.FindAllBadWords(matrix, bannedWords);
    }

    public static List<BadWordMatch> FindBadWordsInMatrix(FillMatrix matrix, List<string> bannedWords)
    {
        return BadWordValidationService.FindAllBadWords(matrix, bannedWords);
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Networking;
using Cysharp.Threading.Tasks;

namespace LevelEditor.Services
{
    /// <summary>
    /// Сервис для валидации слов через Yandex Dictionary API
    /// </summary>
    public class YandexDictionaryService : IWordValidationService
    {
        private const string YANDEX_DICT_API_URL = "https://dictionary.yandex.net/api/v1/dicservice.json/lookup";
        private const string LANG_PAIR = "ru-ru"; // Русский-русский для проверки существования слова

        private readonly string _apiKey;
        private readonly Dictionary<string, bool> _validationCache;
        private readonly HashSet<string> _invalidWords;
        private readonly int _requestTimeout;
        private readonly ValidationServiceSettings _settings;

        public string ServiceName => "Yandex Dictionary";
        public int MinWordLength => 2;
        public int MaxWordLength => 20;

        public YandexDictionaryService(string apiKey, int timeoutSeconds = 10)
            : this(apiKey, timeoutSeconds, new ValidationServiceSettings())
        {
        }

        public YandexDictionaryService(string apiKey, int timeoutSeconds, ValidationServiceSettings settings)
        {
            _apiKey = apiKey;
            _requestTimeout = timeoutSeconds;
            _settings = settings ?? new ValidationServiceSettings();
            _validationCache = new Dictionary<string, bool>();
            _invalidWords = new HashSet<string>();
        }
        
        public async UniTask<bool> IsServiceAvailableAsync()
        {
            if (string.IsNullOrEmpty(_apiKey))
            {
                Debug.LogWarning("Yandex Dictionary API key is not set");
                return false;
            }
            
            try
            {
                // Тестируем с простым словом
                return await IsValidWordAsync("тест");
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Yandex Dictionary service unavailable: {ex.Message}");
                return false;
            }
        }
        
        public async UniTask<bool> IsValidWordAsync(string word)
        {
            // Отладка отключена для производительности
            // if (word.ToUpper() == "УМ" || word.ToUpper() == "ЯД" || word.ToUpper() == "УЖ" || word.ToUpper() == "ЁЖ")
            // {
            //     Debug.Log($"🔍 DEBUG: Starting validation for word '{word}'");
            // }

            if (string.IsNullOrEmpty(word) || word.Length < MinWordLength || word.Length > MaxWordLength)
            {
                // Debug отключен
                // if (word.ToUpper() == "УМ" || word.ToUpper() == "ЯД" || word.ToUpper() == "УЖ" || word.ToUpper() == "ЁЖ")
                // {
                //     Debug.Log($"❌ DEBUG: Word '{word}' failed length check. Length: {word?.Length}, Min: {MinWordLength}, Max: {MaxWordLength}");
                // }
                return false;
            }

            if (string.IsNullOrEmpty(_apiKey))
            {
                // Debug отключен
                // if (word.ToUpper() == "УМ" || word.ToUpper() == "ЯД" || word.ToUpper() == "УЖ" || word.ToUpper() == "ЁЖ")
                // {
                //     Debug.Log($"❌ DEBUG: Word '{word}' failed - no API key");
                // }
                return false;
            }

            // Проверяем, что слово состоит только из русских букв (если включена настройка)
            if (_settings.RequireRussianLettersOnly && !IsRussianWord(word))
            {
                Debug.Log($"❌ Word '{word}' rejected - not a Russian word");
                return false;
            }
            
            string upperWord = word.ToUpper();
            
            // Проверяем кэш
            if (_validationCache.ContainsKey(upperWord))
            {
                // Debug отключен
                // if (word.ToUpper() == "УМ" || word.ToUpper() == "ЯД" || word.ToUpper() == "УЖ" || word.ToUpper() == "ЁЖ")
                // {
                //     Debug.Log($"🔍 DEBUG: Word '{word}' found in cache: {_validationCache[upperWord]}");
                // }
                return _validationCache[upperWord];
            }

            if (_invalidWords.Contains(upperWord))
            {
                // Debug отключен
                // if (word.ToUpper() == "УМ" || word.ToUpper() == "ЯД" || word.ToUpper() == "УЖ" || word.ToUpper() == "ЁЖ")
                // {
                //     Debug.Log($"❌ DEBUG: Word '{word}' found in invalid words cache");
                // }
                return false;
            }
            
            try
            {
                string url = $"{YANDEX_DICT_API_URL}?key={_apiKey}&lang={LANG_PAIR}&text={UnityWebRequest.EscapeURL(word)}";
                
                using (UnityWebRequest request = UnityWebRequest.Get(url))
                {
                    request.timeout = _requestTimeout;
                    await request.SendWebRequest().ToUniTask();
                    
                    if (request.result == UnityWebRequest.Result.Success)
                    {
                        string jsonResponse = request.downloadHandler.text;
                        bool isValid = ParseYandexResponse(jsonResponse, word);
                        
                        // Кэшируем результат
                        if (isValid)
                        {
                            _validationCache[upperWord] = true;
                        }
                        else
                        {
                            _invalidWords.Add(upperWord);
                            _validationCache[upperWord] = false;
                        }
                        
                        return isValid;
                    }
                    else
                    {
                        Debug.LogWarning($"Yandex Dictionary API error: {request.error}");
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Yandex Dictionary API exception for word '{word}': {ex.Message}");
                return false;
            }
        }
        
        public async UniTask<Dictionary<string, bool>> ValidateWordsAsync(IEnumerable<string> words)
        {
            var results = new Dictionary<string, bool>();
            var wordsToCheck = new List<string>();
            
            // Сначала проверяем кэш
            foreach (string word in words)
            {
                string upperWord = word.ToUpper();
                if (_validationCache.ContainsKey(upperWord))
                {
                    results[upperWord] = _validationCache[upperWord];
                }
                else if (_invalidWords.Contains(upperWord))
                {
                    results[upperWord] = false;
                }
                else
                {
                    wordsToCheck.Add(word);
                }
            }
            
            // Проверяем оставшиеся слова с задержкой между запросами
            foreach (string word in wordsToCheck)
            {
                bool isValid = await IsValidWordAsync(word);
                results[word.ToUpper()] = isValid;
                
                // Задержка между запросами для соблюдения лимитов API
                await UniTask.Delay(200);
            }
            
            return results;
        }
        
        public void ClearCache()
        {
            int cacheSize = _validationCache.Count + _invalidWords.Count;
            _validationCache.Clear();
            _invalidWords.Clear();
            Debug.Log($"🧹 Yandex Dictionary cache cleared ({cacheSize} entries removed)");
        }
        
        public string GetCacheStatistics()
        {
            return $"Valid words: {_validationCache.Count(kv => kv.Value)}, Invalid words: {_invalidWords.Count}, Total cache: {_validationCache.Count}";
        }
        
        /// <summary>
        /// Парсит ответ от Yandex Dictionary API
        /// </summary>
        private bool ParseYandexResponse(string jsonResponse, string originalWord)
        {
            try
            {
                // Отладочная информация (отключена для производительности)
                // Debug.Log($"🔍 Parsing response for '{originalWord}': {jsonResponse}");

                // Проверяем, есть ли определения в ответе
                if (!jsonResponse.Contains("\"def\":[") || jsonResponse.Contains("\"def\":[]"))
                {
                    Debug.Log($"❌ No definitions found for '{originalWord}'");
                    return false;
                }

                // Ищем массив определений
                int defStart = jsonResponse.IndexOf("\"def\":[");
                if (defStart == -1)
                {
                    Debug.Log($"❌ No 'def' array found for '{originalWord}'");
                    return false;
                }

                // Ищем закрывающую скобку массива def, учитывая вложенность
                int defEnd = FindClosingBracket(jsonResponse, defStart + 6); // +6 для "def":[
                if (defEnd == -1)
                {
                    Debug.Log($"❌ Malformed 'def' array for '{originalWord}'");
                    return false;
                }

                // Извлекаем содержимое массива def (без скобок)
                string defSection = jsonResponse.Substring(defStart + 7, defEnd - defStart - 7);
                if (string.IsNullOrWhiteSpace(defSection))
                {
                    Debug.Log($"❌ Empty definitions section for '{originalWord}'");
                    return false;
                }

                // Debug.Log($"🔍 Definitions section for '{originalWord}': {defSection}");

                // Парсим каждое определение и проверяем части речи
                return ParseDefinitionsAndFilterByPos(defSection, originalWord);
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Error parsing Yandex response for '{originalWord}': {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Парсит определения и фильтрует по частям речи
        /// </summary>
        private bool ParseDefinitionsAndFilterByPos(string defSection, string originalWord)
        {
            try
            {
                Debug.Log($"🔍 Parsing definitions section: {defSection}");

                // Разбиваем на отдельные определения (каждое определение в фигурных скобках)
                var definitions = SplitDefinitions(defSection);
                Debug.Log($"🔍 Found {definitions.Count} definitions");

                foreach (string definition in definitions)
                {
                    if (string.IsNullOrWhiteSpace(definition)) continue;
                    Debug.Log($"🔍 Processing definition: {definition}");

                    // Ищем часть речи в определении
                    string pos = ExtractPartOfSpeech(definition);

                    if (!string.IsNullOrEmpty(pos))
                    {
                        Debug.Log($"🔍 Found POS '{pos}' for word '{originalWord}'");

                        if (IsValidPartOfSpeech(pos))
                        {
                            // Проверяем качество слова на основе определения
                            if (IsQualityWord(originalWord, definition))
                            {
                                Debug.Log($"✅ Word '{originalWord}' validated with POS: {pos}");
                                return true;
                            }
                            else
                            {
                                Debug.Log($"❌ Word '{originalWord}' rejected by quality filter");

                                // Debug отключен
                                // if (originalWord.ToUpper() == "УМ" || originalWord.ToUpper() == "ЯД" ||
                                //     originalWord.ToUpper() == "УЖ" || originalWord.ToUpper() == "ЁЖ")
                                // {
                                //     Debug.Log($"🚨 DEBUG: Good word '{originalWord}' was rejected! Definition: {definition}");
                                // }
                            }
                        }
                        else
                        {
                            Debug.Log($"❌ Word '{originalWord}' rejected - invalid POS: {pos}");
                        }
                    }
                    else
                    {
                        Debug.Log($"❌ No POS found in definition for '{originalWord}'");
                    }
                }

                Debug.Log($"❌ Word '{originalWord}' rejected - no valid parts of speech found");
                return false;
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Error parsing definitions for '{originalWord}': {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Находит закрывающую скобку массива, учитывая вложенность
        /// </summary>
        private int FindClosingBracket(string json, int startIndex)
        {
            int bracketCount = 0;
            bool inString = false;
            bool escapeNext = false;

            for (int i = startIndex; i < json.Length; i++)
            {
                char c = json[i];

                if (escapeNext)
                {
                    escapeNext = false;
                    continue;
                }

                if (c == '\\')
                {
                    escapeNext = true;
                    continue;
                }

                if (c == '"')
                {
                    inString = !inString;
                    continue;
                }

                if (!inString)
                {
                    if (c == '[')
                    {
                        bracketCount++;
                    }
                    else if (c == ']')
                    {
                        bracketCount--;
                        if (bracketCount == 0)
                        {
                            return i;
                        }
                    }
                }
            }

            return -1; // Не найдена закрывающая скобка
        }

        /// <summary>
        /// Разбивает строку определений на отдельные определения
        /// </summary>
        private List<string> SplitDefinitions(string defSection)
        {
            var definitions = new List<string>();
            int braceCount = 0;
            int start = 0;

            for (int i = 0; i < defSection.Length; i++)
            {
                if (defSection[i] == '{')
                {
                    if (braceCount == 0) start = i;
                    braceCount++;
                }
                else if (defSection[i] == '}')
                {
                    braceCount--;
                    if (braceCount == 0)
                    {
                        definitions.Add(defSection.Substring(start, i - start + 1));
                    }
                }
            }

            return definitions;
        }

        /// <summary>
        /// Извлекает часть речи из определения
        /// </summary>
        private string ExtractPartOfSpeech(string definition)
        {
            // Debug.Log($"🔍 Extracting POS from definition: {definition}");

            // Ищем поле "pos" в JSON
            int posStart = definition.IndexOf("\"pos\":\"");
            if (posStart == -1)
            {
                Debug.Log($"❌ No 'pos' field found in definition");
                return null;
            }

            posStart += 7; // Длина "pos":"
            int posEnd = definition.IndexOf("\"", posStart);
            if (posEnd == -1)
            {
                Debug.Log($"❌ Malformed 'pos' field in definition");
                return null;
            }

            string pos = definition.Substring(posStart, posEnd - posStart);
            // Debug.Log($"🔍 Extracted POS: '{pos}'");
            return pos;
        }

        /// <summary>
        /// Проверяет, является ли часть речи допустимой (исключает междометия и редкие слова)
        /// </summary>
        private bool IsValidPartOfSpeech(string pos)
        {
            if (string.IsNullOrEmpty(pos)) return false;

            // Создаем список недопустимых частей речи на основе настроек
            var invalidPos = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            if (_settings.FilterInterjections)
            {
                invalidPos.Add("interjection");
                invalidPos.Add("interj");
                invalidPos.Add("междометие");
            }

            if (_settings.FilterParticles)
            {
                invalidPos.Add("particle");
                invalidPos.Add("part");
                invalidPos.Add("частица");
            }

            if (_settings.FilterAbbreviations)
            {
                invalidPos.Add("abbreviation");
                invalidPos.Add("abbr");
                invalidPos.Add("аббревиатура");
                invalidPos.Add("acronym");
                invalidPos.Add("акроним");
            }

            // Всегда исключаем префиксы, суффиксы и символы
            invalidPos.Add("prefix");
            invalidPos.Add("suffix");
            invalidPos.Add("префикс");
            invalidPos.Add("суффикс");
            invalidPos.Add("symbol");
            invalidPos.Add("символ");
            invalidPos.Add("onomatopoeia");
            invalidPos.Add("звукоподражание");

            return !invalidPos.Contains(pos);
        }

        /// <summary>
        /// Дополнительная проверка качества слова (исключает редкие и неподходящие слова)
        /// </summary>
        private bool IsQualityWord(string word, string definition)
        {
            if (string.IsNullOrEmpty(word) || string.IsNullOrEmpty(definition))
                return false;

            // Создаем список нежелательных тегов на основе настроек
            var unwantedTags = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            if (_settings.FilterObsoleteWords)
            {
                unwantedTags.Add("obsolete");
                unwantedTags.Add("archaic");
                unwantedTags.Add("устаревшее");
                unwantedTags.Add("архаичное");
                unwantedTags.Add("rare");
                unwantedTags.Add("редкое");
            }

            if (_settings.FilterSlangWords)
            {
                unwantedTags.Add("slang");
                unwantedTags.Add("сленг");
                unwantedTags.Add("colloquial");
                unwantedTags.Add("разговорное");
            }

            if (_settings.FilterVulgarWords)
            {
                unwantedTags.Add("vulgar");
                unwantedTags.Add("вульгарное");
                unwantedTags.Add("offensive");
                unwantedTags.Add("оскорбительное");
            }

            if (_settings.FilterTechnicalTerms)
            {
                unwantedTags.Add("technical");
                unwantedTags.Add("техническое");
                unwantedTags.Add("specialized");
                unwantedTags.Add("специализированное");
            }

            if (_settings.FilterDialectalWords)
            {
                unwantedTags.Add("dialectal");
                unwantedTags.Add("диалектное");
                unwantedTags.Add("regional");
                unwantedTags.Add("региональное");
            }

            // Проверяем наличие нежелательных тегов в определении
            foreach (var tag in unwantedTags)
            {
                if (definition.Contains(tag))
                {
                    Debug.Log($"❌ Word '{word}' rejected - contains unwanted tag: {tag}");
                    return false;
                }
            }

            // Исключаем слова с цифрами или специальными символами (если включена настройка)
            if (_settings.RequireRussianLettersOnly && word.Any(c => char.IsDigit(c) || (!char.IsLetter(c) && c != 'ё' && c != 'Ё')))
            {
                Debug.Log($"❌ Word '{word}' rejected - contains digits or special characters");
                return false;
            }

            // Проверяем длину слова согласно настройкам
            if (word.Length < _settings.MinBonusWordLength || word.Length > _settings.MaxBonusWordLength)
            {
                Debug.Log($"❌ Word '{word}' rejected - inappropriate length: {word.Length} (allowed: {_settings.MinBonusWordLength}-{_settings.MaxBonusWordLength})");
                return false;
            }

            // Исключаем только явные междометия и неподходящие слова
            var unwantedShortWords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                // Междометия и восклицания
                "АХ", "ОХ", "УХ", "ЭХ", "ИХ", "ОЙ", "АЙ", "УЙ", "ЭЙ",
                "ХА", "ХИ", "ХЕ", "ХО", "ХЫ",
                "ОМ", "ЭМ", "АМ", "ИМ", "ЫМ",
                "УРА", "ГОП", "ОПА", "ЭГЕ", "ИГО",

                // Звукоподражания
                "МУ", "МЯ", "ГАВ", "МЯУ", "КУ", "КО", "ПИ",

                // Сомнительные/неестественные слова
                "МОМ", "ПОП", "БОБ", "ТОТ", "КОК", "ЛОЛ", "НОН", "ТУТ",

                // Служебные слова и предлоги (только самые короткие)
                "ОБ", "ОТ", "ИЗ", "ЗА", "НА", "ПО", "ДО", "ВО", "СО",
                "ЛИ", "ЖЕ", "БЫ", "НЕ", "НИ",

                // Местоимения и указательные слова
                "ТО", "ТА", "ТИ", "ТЫ", "ТЕ", "МЫ", "ВЫ", "ОН", "ОНА"
            };

            if (unwantedShortWords.Contains(word.ToUpper()))
            {
                Debug.Log($"❌ Word '{word}' rejected - unwanted short word/interjection");
                return false;
            }

            // Debug отключен
            // if (word.ToUpper() == "УМ" || word.ToUpper() == "ЯД" || word.ToUpper() == "УЖ" || word.ToUpper() == "ЁЖ")
            // {
            //     Debug.Log($"🔍 DEBUG: Word '{word}' passed quality filter - should be VALID");
            // }

            return true;
        }
        
        /// <summary>
        /// Проверяет, является ли слово русским
        /// </summary>
        private bool IsRussianWord(string word)
        {
            if (string.IsNullOrEmpty(word))
                return false;
                
            foreach (char c in word)
            {
                if (!((c >= 'а' && c <= 'я') || (c >= 'А' && c <= 'Я') || c == 'ё' || c == 'Ё'))
                    return false;
            }
            
            return true;
        }
    }
}

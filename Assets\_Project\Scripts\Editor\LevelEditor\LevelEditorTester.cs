using UnityEditor;

namespace LevelEditor
{
    public static class LevelEditorTester
    {
        [<PERSON><PERSON><PERSON><PERSON>("Tools/Level Editor")]
        public static void OpenLevelEditor()
        {
            LevelEditorWindow.ShowWindow();
        }

        [<PERSON>u<PERSON><PERSON>("Tools/Level Editor/Main Editor Window")]
        public static void OpenMainLevelEditor()
        {
            LevelEditorWindow.ShowWindow();
        }

        [<PERSON>u<PERSON><PERSON>("Window/Level Editor")]
        public static void OpenLevelEditorWindow()
        {
            LevelEditorWindow.ShowWindow();
        }
    }
}

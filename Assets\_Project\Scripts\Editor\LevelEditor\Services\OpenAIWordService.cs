using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using UnityEngine.Networking;
using Cysharp.Threading.Tasks;

namespace LevelEditor.Services
{
    /// <summary>
    /// Структуры для сериализации запросов к OpenAI API
    /// </summary>
    [System.Serializable]
    public class OpenAIMessage
    {
        public string role;
        public string content;

        public OpenAIMessage(string role, string content)
        {
            this.role = role;
            this.content = content;
        }
    }

    [System.Serializable]
    public class OpenAIRequest
    {
        public string model;
        public OpenAIMessage[] messages;
        public int max_tokens;
        public float temperature;

        public OpenAIRequest(string model, OpenAIMessage[] messages, int maxTokens, float temperature)
        {
            this.model = model;
            this.messages = messages;
            this.max_tokens = maxTokens;
            this.temperature = temperature;
        }
    }

    /// <summary>
    /// Сервис для валидации слов через OpenAI API
    /// </summary>
    public class OpenAIWordService : IWordValidationService
    {
        private const string OPENAI_API_URL = "https://api.openai.com/v1/chat/completions";
        private const string MODEL = "gpt-3.5-turbo";
        
        private readonly string _apiKey;
        private readonly Dictionary<string, WordValidationResult> _validationCache;
        private readonly int _requestTimeout;
        
        public string ServiceName => "OpenAI ChatGPT";
        public int MinWordLength => 2;
        public int MaxWordLength => 25;
        
        public OpenAIWordService(string apiKey, int timeoutSeconds = 15)
        {
            _apiKey = apiKey;
            _requestTimeout = timeoutSeconds;
            _validationCache = new Dictionary<string, WordValidationResult>();
        }
        
        public async UniTask<bool> IsServiceAvailableAsync()
        {
            if (string.IsNullOrEmpty(_apiKey))
            {
                Debug.LogWarning("OpenAI API key is not set");
                return false;
            }

            try
            {
                // Тестируем с простым запросом
                var testResult = await ValidateWordWithAI("тест");
                return testResult != null;
            }
            catch (Exception ex)
            {
                LogOpenAIError(ex.Message, "service availability check");
                return false;
            }
        }

        /// <summary>
        /// Логирует ошибки OpenAI с понятными сообщениями
        /// </summary>
        private void LogOpenAIError(string errorMessage, string context = "")
        {
            if (errorMessage.Contains("insufficient_quota") || errorMessage.Contains("429"))
            {
                Debug.LogWarning($"OpenAI {context}: Превышена квота API. Проверьте баланс на https://platform.openai.com/account/billing");
            }
            else if (errorMessage.Contains("invalid_api_key") || errorMessage.Contains("401"))
            {
                Debug.LogWarning($"OpenAI {context}: Неверный API ключ. Проверьте настройки.");
            }
            else if (errorMessage.Contains("rate_limit") || errorMessage.Contains("429"))
            {
                Debug.LogWarning($"OpenAI {context}: Превышен лимит запросов. Попробуйте позже.");
            }
            else
            {
                Debug.LogWarning($"OpenAI {context}: {errorMessage}");
            }
        }
        
        public async UniTask<bool> IsValidWordAsync(string word)
        {
            if (string.IsNullOrEmpty(word) || word.Length < MinWordLength || word.Length > MaxWordLength)
                return false;
                
            if (string.IsNullOrEmpty(_apiKey))
                return false;
            
            string upperWord = word.ToUpper();
            
            // Проверяем кэш
            if (_validationCache.ContainsKey(upperWord))
                return _validationCache[upperWord].IsValid;
            
            try
            {
                var result = await ValidateWordWithAI(word);
                if (result != null)
                {
                    _validationCache[upperWord] = result;
                    return result.IsValid;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                LogOpenAIError(ex.Message, $"word validation for '{word}'");
                return false;
            }
        }
        
        public async UniTask<Dictionary<string, bool>> ValidateWordsAsync(IEnumerable<string> words)
        {
            var results = new Dictionary<string, bool>();
            var wordsToCheck = new List<string>();
            
            // Сначала проверяем кэш
            foreach (string word in words)
            {
                string upperWord = word.ToUpper();
                if (_validationCache.ContainsKey(upperWord))
                {
                    results[upperWord] = _validationCache[upperWord].IsValid;
                }
                else
                {
                    wordsToCheck.Add(word);
                }
            }
            
            // Проверяем оставшиеся слова пакетами для экономии токенов
            if (wordsToCheck.Count > 0)
            {
                var batchResults = await ValidateWordsBatch(wordsToCheck);
                foreach (var kvp in batchResults)
                {
                    results[kvp.Key.ToUpper()] = kvp.Value;
                }
            }
            
            return results;
        }
        
        public void ClearCache()
        {
            int cacheSize = _validationCache.Count;
            _validationCache.Clear();
            Debug.Log($"🧹 OpenAI cache cleared ({cacheSize} entries removed)");
        }
        
        public string GetCacheStatistics()
        {
            int validCount = _validationCache.Count(kv => kv.Value.IsValid);
            int invalidCount = _validationCache.Count - validCount;
            return $"Valid words: {validCount}, Invalid words: {invalidCount}, Total cache: {_validationCache.Count}";
        }
        
        /// <summary>
        /// Валидирует одно слово через OpenAI API
        /// </summary>
        private async UniTask<WordValidationResult> ValidateWordWithAI(string word)
        {
            string prompt = $"Является ли слово '{word}' существующим русским словом? Ответь только 'да' или 'нет' без объяснений.";

            var requestData = new OpenAIRequest(
                MODEL,
                new OpenAIMessage[] { new OpenAIMessage("user", prompt) },
                10,
                0.1f
            );

            string jsonRequest = JsonUtility.ToJson(requestData);

            using (UnityWebRequest request = new UnityWebRequest(OPENAI_API_URL, "POST"))
            {
                byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonRequest);
                request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                request.downloadHandler = new DownloadHandlerBuffer();
                request.SetRequestHeader("Content-Type", "application/json");
                request.SetRequestHeader("Authorization", $"Bearer {_apiKey}");
                request.timeout = _requestTimeout;
                
                await request.SendWebRequest().ToUniTask();
                
                if (request.result == UnityWebRequest.Result.Success)
                {
                    string response = request.downloadHandler.text;
                    return ParseOpenAIResponse(response, word);
                }
                else
                {
                    string errorResponse = request.downloadHandler?.text ?? "No response";
                    Debug.LogWarning($"OpenAI API error: {request.error}");
                    Debug.LogWarning($"OpenAI Response: {errorResponse}");
                    return null;
                }
            }
        }
        
        /// <summary>
        /// Валидирует несколько слов одним запросом
        /// </summary>
        private async UniTask<Dictionary<string, bool>> ValidateWordsBatch(List<string> words)
        {
            var results = new Dictionary<string, bool>();
            
            if (words.Count == 0)
                return results;
            
            // Ограничиваем размер пакета для экономии токенов
            const int batchSize = 10;
            
            for (int i = 0; i < words.Count; i += batchSize)
            {
                var batch = words.Skip(i).Take(batchSize).ToList();
                string wordsList = string.Join(", ", batch);
                
                string prompt = $"Для каждого из следующих слов укажи, является ли оно существующим русским словом. Слова: {wordsList}. " +
                               "Ответь в формате: слово1:да/нет, слово2:да/нет, и т.д.";

                var requestData = new OpenAIRequest(
                    MODEL,
                    new OpenAIMessage[] { new OpenAIMessage("user", prompt) },
                    100,
                    0.1f
                );

                string jsonRequest = JsonUtility.ToJson(requestData);
                
                using (UnityWebRequest request = new UnityWebRequest(OPENAI_API_URL, "POST"))
                {
                    byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonRequest);
                    request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                    request.downloadHandler = new DownloadHandlerBuffer();
                    request.SetRequestHeader("Content-Type", "application/json");
                    request.SetRequestHeader("Authorization", $"Bearer {_apiKey}");
                    request.timeout = _requestTimeout;
                    
                    await request.SendWebRequest().ToUniTask();
                    
                    if (request.result == UnityWebRequest.Result.Success)
                    {
                        string response = request.downloadHandler.text;
                        var batchResults = ParseBatchResponse(response, batch);
                        
                        foreach (var kvp in batchResults)
                        {
                            results[kvp.Key] = kvp.Value;
                            
                            // Кэшируем результат
                            _validationCache[kvp.Key.ToUpper()] = new WordValidationResult(
                                kvp.Key, kvp.Value, ServiceName, 0.8f);
                        }
                    }
                    
                    // Задержка между пакетными запросами
                    await UniTask.Delay(1000);
                }
            }
            
            return results;
        }
        
        /// <summary>
        /// Парсит ответ OpenAI для одного слова
        /// </summary>
        private WordValidationResult ParseOpenAIResponse(string jsonResponse, string originalWord)
        {
            try
            {
                // Простой парсинг JSON ответа
                if (jsonResponse.Contains("\"content\""))
                {
                    int contentStart = jsonResponse.IndexOf("\"content\":\"") + 11;
                    if (contentStart > 10)
                    {
                        int contentEnd = jsonResponse.IndexOf("\"", contentStart);
                        if (contentEnd > contentStart)
                        {
                            string content = jsonResponse.Substring(contentStart, contentEnd - contentStart).ToLower();
                            bool isValid = content.Contains("да") || content.Contains("yes");
                            
                            return new WordValidationResult(originalWord, isValid, ServiceName, 0.9f);
                        }
                    }
                }
                
                return new WordValidationResult(originalWord, false, ServiceName, 0.1f);
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Error parsing OpenAI response for '{originalWord}': {ex.Message}");
                return new WordValidationResult(originalWord, false, ServiceName, 0.0f);
            }
        }
        
        /// <summary>
        /// Парсит ответ OpenAI для пакета слов
        /// </summary>
        private Dictionary<string, bool> ParseBatchResponse(string jsonResponse, List<string> originalWords)
        {
            var results = new Dictionary<string, bool>();
            
            try
            {
                if (jsonResponse.Contains("\"content\""))
                {
                    int contentStart = jsonResponse.IndexOf("\"content\":\"") + 11;
                    if (contentStart > 10)
                    {
                        int contentEnd = jsonResponse.IndexOf("\"", contentStart);
                        if (contentEnd > contentStart)
                        {
                            string content = jsonResponse.Substring(contentStart, contentEnd - contentStart);
                            
                            // Парсим ответ вида "слово1:да, слово2:нет"
                            var pairs = content.Split(',');
                            foreach (var pair in pairs)
                            {
                                var parts = pair.Split(':');
                                if (parts.Length == 2)
                                {
                                    string word = parts[0].Trim();
                                    string answer = parts[1].Trim().ToLower();
                                    bool isValid = answer.Contains("да") || answer.Contains("yes");
                                    
                                    results[word] = isValid;
                                }
                            }
                        }
                    }
                }
                
                // Если не удалось распарсить, помечаем все слова как невалидные
                foreach (string word in originalWords)
                {
                    if (!results.ContainsKey(word))
                    {
                        results[word] = false;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Error parsing OpenAI batch response: {ex.Message}");
                
                // В случае ошибки помечаем все слова как невалидные
                foreach (string word in originalWords)
                {
                    results[word] = false;
                }
            }
            
            return results;
        }
    }
}

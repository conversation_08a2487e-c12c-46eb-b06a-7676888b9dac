# Bad Word Validation System

Централизованная система проверки нецензурных слов в матрицах уровней.

## Архитектура

**BadWordValidationService** - центральный сервис для всех проверок
- Единая логика проверки по всем 8 направлениям
- Используется в `LevelsPregenerator` через `LevelsPregeneratorExtension`
- Заменяет старую систему проверки только по горизонтали/вертикали

**BadWordChecker** - статический класс с Editor окном
- Автоматически загружает данные из фиксированных путей
- Не требует создания GameObject в сцене
- Все операции через Editor окно

## Использование

1. `Tools → Bad Word Checker` - открывает полноэкранное окно с темным интерфейсом
2. Нажмите кнопку "🔍 Check All Levels" для сканирования всех уровней
3. Просмотр результатов в удобном интерфейсе:
   - **Синий заголовок** с названием и описанием системы
   - **Темный фон** для комфортной работы
   - **Цветовые индикаторы серьезности** (красный/оранжевый/зеленый)
   - **Контрастный текст** - белый, красный, голубой, желтый для разных типов информации
   - **Карточки уровней** с детальной информацией о найденных словах
   - Быстрый переход к проблемным файлам через кнопку "Select"
4. Кнопки управления:
   - **"Clear Results"** - очистка результатов сканирования
   - **"Export"** - вывод результатов в консоль Unity
   - **"Regenerate Levels"** - автоматическая перегенерация уровней с нецензурными словами

## Автоматическая перегенерация

Функция "Regenerate Levels" позволяет автоматически исправить проблемные уровни:

1. **Активируется только при наличии найденных проблем** - кнопка доступна только после сканирования
2. **Показывает диалог подтверждения** с списком уровней для перегенерации
3. **Прогресс-бар** показывает процесс обработки каждого уровня
4. **Автоматическая проверка** - новые матрицы генерируются без нецензурных слов
5. **Сохранение изменений** - все изменения автоматически сохраняются в ассеты

### Процесс перегенерации:
- Загружается LevelPreGenerator из префаба
- Для каждого проблемного уровня генерируются новые матрицы
- Каждая матрица проверяется на отсутствие запрещенных слов
- Результаты сохраняются в соответствующие LevelsSO ассеты

## Файлы

- `BadWordValidationService.cs` - центральный сервис
- `BadWordChecker.cs` - статический класс с логикой
- `BadWordCheckerEditor.cs` - Editor окно
- `LevelsPregeneratorExtension.cs` - интеграция с генератором

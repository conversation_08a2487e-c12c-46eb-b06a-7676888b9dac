using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using Cysharp.Threading.Tasks;

namespace LevelEditor.Services
{
    public class BadWordValidationServiceEditor : BaseValidationService
    {
        private const string BANNED_DATA_PATH = "Assets/_Project/Prefabs/SO/tables/BannedData.asset";
        
        public override string ServiceName => "Bad Words Validation";
        
        private BannedData bannedData;
        
        public BadWordValidationServiceEditor()
        {
            LoadBannedData();
        }
        
        private void LoadBannedData()
        {
            bannedData = AssetDatabase.LoadAssetAtPath<BannedData>(BANNED_DATA_PATH);
            if (bannedData == null)
            {
                Debug.LogError($"BannedData not found at path: {BANNED_DATA_PATH}");
            }
        }
        
        protected override async UniTask ValidateInternalAsync(LevelsSO level, ValidationResult result, IProgress<ValidationProgress> progress)
        {
            if (bannedData == null)
            {
                result.IsValid = false;
                result.Errors.Add(new ValidationError("Configuration", "BannedData asset not found"));
                return;
            }
            
            if (level.FillMatrixes == null || level.FillMatrixes.Count == 0)
            {
                result.IsValid = false;
                result.Errors.Add(new ValidationError("Data", "Level has no matrices to validate"));
                return;
            }
            
            ReportProgress(progress, "Starting bad word validation", 0, level.FillMatrixes.Count);
            
            for (int matrixIndex = 0; matrixIndex < level.FillMatrixes.Count; matrixIndex++)
            {
                ReportProgress(progress, $"Checking matrix {matrixIndex + 1}", matrixIndex, level.FillMatrixes.Count);
                
                var matrix = level.FillMatrixes[matrixIndex];
                var matches = global::BadWordValidationService.FindAllBadWords(matrix, bannedData.Words);
                
                foreach (var match in matches)
                {
                    result.IsValid = false;
                    result.Errors.Add(new ValidationError(
                        "BadWord", 
                        $"Found bad word '{match.word}' in matrix {matrixIndex}",
                        matrixIndex,
                        $"Position: {match.startPosition} -> {match.endPosition}, Direction: {match.direction}"
                    ));
                }
                
                await DelayForUI();
            }
            
            ReportProgress(progress, "Bad word validation completed", level.FillMatrixes.Count, level.FillMatrixes.Count);
        }
        
        protected override string CreateSummary(ValidationResult result)
        {
            if (result.IsValid)
            {
                return $"{ServiceName}: No bad words found";
            }
            else
            {
                int badWordCount = result.Errors.Count;
                return $"{ServiceName}: Found {badWordCount} bad word(s)";
            }
        }
    }
}

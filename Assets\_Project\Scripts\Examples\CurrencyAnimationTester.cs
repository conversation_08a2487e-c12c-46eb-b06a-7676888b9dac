using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class CurrencyAnimationTester : MonoBehaviour
{
    [Header("Test Buttons")]
    [SerializeField] private Button _testAddButton;
    [SerializeField] private Button _testAddWithAnimationButton;
    [SerializeField] private Button _testAddWithoutAnimationButton;
    
    [Header("Info")]
    [SerializeField] private TextMeshProUGUI _balanceText;
    [SerializeField] private TextMeshProUGUI _statusText;
    
    [Header("Settings")]
    [SerializeField] private int _testAmount = 50;

    private void Start()
    {
        CurrencyManager.OnBalanceChanged += OnBalanceChanged;
        
        if (_testAddButton != null)
            _testAddButton.onClick.AddListener(() => TestAdd());
            
        if (_testAddWithAnimationButton != null)
            _testAddWithAnimationButton.onClick.AddListener(() => TestAddWithAnimation());
            
        if (_testAddWithoutAnimationButton != null)
            _testAddWithoutAnimationButton.onClick.AddListener(() => TestAddWithoutAnimation());
        
        if (PayrollAnimation.Instance != null)
        {
            PayrollAnimation.Instance.OnComplete += OnAnimationComplete;
            UpdateStatus("✅ Payroll_Animation найден");
        }
        else
        {
            UpdateStatus("❌ Payroll_Animation НЕ найден!");
        }
        
        UpdateBalanceDisplay();
    }

    private void OnDestroy()
    {
        CurrencyManager.OnBalanceChanged -= OnBalanceChanged;
        
        if (PayrollAnimation.Instance != null)
        {
            PayrollAnimation.Instance.OnComplete -= OnAnimationComplete;
        }
    }

    private void TestAdd()
    {
        UpdateStatus($"🧪 Тест Add({_testAmount})");
        CurrencyManager.Add(_testAmount);
    }

    private void TestAddWithAnimation()
    {
        UpdateStatus($"🧪 Тест AddWithAnimation({_testAmount})");
        var buttonRect = _testAddWithAnimationButton.GetComponent<RectTransform>();
        CurrencyManager.AddWithAnimation(buttonRect, _testAmount);
    }

    private void TestAddWithoutAnimation()
    {
        UpdateStatus($"🧪 Тест AddWithoutAnimation({_testAmount})");
        CurrencyManager.AddWithoutAnimation(_testAmount);
    }

    private void OnAnimationComplete(int amount)
    {
        UpdateStatus($"✅ Анимация завершена: +{amount}");
        Debug.Log($"💰 Анимация завершена: +{amount}");
    }

    private void OnBalanceChanged(int newBalance)
    {
        UpdateBalanceDisplay();
        Debug.Log($"💰 Баланс изменен: {newBalance}");
    }

    private void UpdateBalanceDisplay()
    {
        if (_balanceText != null)
            _balanceText.text = $"💰 Баланс: {CurrencyManager.Balance}";
    }

    private void UpdateStatus(string message)
    {
        if (_statusText != null)
            _statusText.text = message;
        Debug.Log(message);
    }
}

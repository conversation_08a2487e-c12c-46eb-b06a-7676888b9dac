using UnityEngine;
using System.Collections;
using UnityEditor;


[CustomEditor(typeof(LevelsGenerator))]
public class LevelGeneratorEditor :  Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        LevelsGenerator myScript = (LevelsGenerator)target;
        /*if (GUILayout.Button("Generate Levels"))
        {
            myScript.GenerateLevels();
        }
*/
        if (GUILayout.Button("Generate Word Distribution"))
        {
            myScript.CreateWordDistribution();
        }
    }

}


using System;
using UnityEngine;
using VG;

public class CurrencyManager : MonoBehaviour
{
    public static event Action<int> OnBalanceChanged;
    public static event Action<int> OnCurrencyAdded;
    public static event Action<int> OnCurrencySpent;
    public static event Action<int> OnInsufficientFunds;

    private static CurrencyManager _instance;

    public static CurrencyManager Instance => _instance;
    public static int Balance => Saves.Int[Key_Save.Currency].Value;

    private void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
            Saves.Int[Key_Save.Currency].onChanged += OnCurrencyValueChanged;
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
        }
    }

    private void OnDestroy()
    {
        if (_instance == this && Saves.Initialized)
            Saves.Int[Key_Save.Currency].onChanged -= OnCurrencyValueChanged;
    }

    private void OnCurrencyValueChanged() => OnBalanceChanged?.Invoke(Balance);

    public static void Add(int amount)
    {
        if (amount <= 0) return;

        if (PayrollAnimation.Instance != null)
        {
            PayrollAnimation.Instance.RunCurrencyAnimation(null, amount);
        }
        else
        {
            AddWithoutAnimation(amount);
        }
    }

    public static void AddWithoutAnimation(int amount)
    {
        if (amount <= 0) return;

        Saves.Int[Key_Save.Currency].Value += amount;
        OnCurrencyAdded?.Invoke(amount);
        Save();
    }

    public static void AddWithAnimation(RectTransform fromPosition, int amount)
    {
        if (amount <= 0) return;

        if (PayrollAnimation.Instance != null)
        {
            PayrollAnimation.Instance.RunCurrencyAnimation(fromPosition, amount);
        }
        else
        {
            AddWithoutAnimation(amount);
        }
    }

    public static bool Spend(int amount)
    {
        if (amount <= 0 || Balance < amount)
        {
            if (amount > 0) OnInsufficientFunds?.Invoke(amount);
            return false;
        }

        Saves.Int[Key_Save.Currency].Value -= amount;
        OnCurrencySpent?.Invoke(amount);
        Save();
        return true;
    }

    public static bool CanAfford(int amount) => Balance >= amount;

    public static void SetBalance(int amount)
    {
        if (amount < 0) return;
        Saves.Int[Key_Save.Currency].Value = amount;
        Save();
    }

    [System.Diagnostics.Conditional("UNITY_EDITOR")]
    public static void ResetCurrency() => SetBalance(0);

    [System.Diagnostics.Conditional("UNITY_EDITOR")]
    public static void AddDebugCurrency(int amount) => Add(amount);

    private static void Save() => Saves.Commit();
}

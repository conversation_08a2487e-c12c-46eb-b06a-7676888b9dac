using System.Collections.Generic;
using Cysharp.Threading.Tasks;

namespace LevelEditor.Services
{
    /// <summary>
    /// Интерфейс для внешних сервисов валидации слов
    /// </summary>
    public interface IWordValidationService
    {
        /// <summary>
        /// Название сервиса
        /// </summary>
        string ServiceName { get; }
        
        /// <summary>
        /// Минимальная длина слова, поддерживаемая сервисом
        /// </summary>
        int MinWordLength { get; }
        
        /// <summary>
        /// Максимальная длина слова, поддерживаемая сервисом
        /// </summary>
        int MaxWordLength { get; }
        
        /// <summary>
        /// Проверяет доступность сервиса
        /// </summary>
        UniTask<bool> IsServiceAvailableAsync();
        
        /// <summary>
        /// Проверяет, является ли слово валидным
        /// </summary>
        /// <param name="word">Слово для проверки</param>
        /// <returns>True, если слово валидно</returns>
        UniTask<bool> IsValidWordAsync(string word);
        
        /// <summary>
        /// Проверяет список слов на валидность (пакетная обработка)
        /// </summary>
        /// <param name="words">Список слов для проверки</param>
        /// <returns>Словарь с результатами проверки</returns>
        UniTask<Dictionary<string, bool>> ValidateWordsAsync(IEnumerable<string> words);
        
        /// <summary>
        /// Очищает кэш сервиса
        /// </summary>
        void ClearCache();
        
        /// <summary>
        /// Получает статистику кэша
        /// </summary>
        string GetCacheStatistics();
    }
    
    /// <summary>
    /// Результат валидации слова
    /// </summary>
    [System.Serializable]
    public class WordValidationResult
    {
        public string Word;
        public bool IsValid;
        public string Source;
        public string ErrorMessage;
        public float ConfidenceScore; // Для AI сервисов
        
        public WordValidationResult(string word, bool isValid, string source = "", float confidence = 1.0f)
        {
            Word = word;
            IsValid = isValid;
            Source = source;
            ConfidenceScore = confidence;
        }
    }
    
    /// <summary>
    /// Настройки для сервисов валидации
    /// </summary>
    [System.Serializable]
    public class ValidationServiceSettings
    {
        public bool UseLocalDictionary = true;
        public bool UseYandexDictionary = false;
        public bool UseOpenAI = false;
        public string YandexApiKey = "";
        public string OpenAIApiKey = "";
        public int RequestTimeoutSeconds = 10;
        public int MaxConcurrentRequests = 5;
        public bool EnableCaching = true;
        public int MinWordLengthForExternal = 2;
        public int MaxWordLengthForExternal = 15;

        // Настройки фильтрации для Yandex Dictionary
        public bool FilterInterjections = true;          // Фильтровать междометия
        public bool FilterParticles = true;              // Фильтровать частицы
        public bool FilterAbbreviations = true;          // Фильтровать аббревиатуры
        public bool FilterObsoleteWords = true;          // Фильтровать устаревшие слова
        public bool FilterSlangWords = true;             // Фильтровать сленг
        public bool FilterVulgarWords = true;            // Фильтровать вульгарные слова
        public bool FilterTechnicalTerms = true;         // Фильтровать технические термины
        public bool FilterDialectalWords = true;         // Фильтровать диалектные слова
        public bool RequireRussianLettersOnly = true;    // Только русские буквы
        public int MinBonusWordLength = 3;               // Минимальная длина бонусного слова
        public int MaxBonusWordLength = 12;              // Максимальная длина бонусного слова
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using Cysharp.Threading.Tasks;
using Random = UnityEngine.Random;

namespace LevelEditor.Services
{
    public partial class LevelGenerationService : ILevelGenerationService
    {
        public static bool PreventStraightLines { get; set; } = true;
        public static float BendPriority { get; set; } = 0.6f;
        public static bool FastGeneration { get; set; } = true;

        // Generation Conditions
        public static bool EnableMatrixUniquenessCheck { get; set; } = true;
        public static bool EnableBadWordCheck { get; set; } = true;
        public static bool EnableMatrixIntegrityCheck { get; set; } = true;
        public static bool EnableStraightLineCheck { get; set; } = true;
        public static bool StrictFirstMatrix { get; set; } = true;

        // Matrix Validation Scopes (using int to avoid enum dependency issues)
        public static int BadWordValidationScope { get; set; } = 1; // AllMatrices
        public static int MatrixIntegrityValidationScope { get; set; } = 1; // AllMatrices
        public static int MatrixUniquenessValidationScope { get; set; } = 1; // AllMatrices
        public static int StraightLineValidationScope { get; set; } = 0; // FirstMatrixOnly

        private const string LEVELS_FOLDER_PATH = "Assets/_Project/Prefabs/SO/Levels";

        private WordDistributionSO wordDistributionSO;
        private LevelsData levelsData;
        private WordsData wordsData;
        private LevelsConfigSO levelsConfig;
        private BannedData badWords;
        
        private int generateCount = 3;
        private int maxAttemptsPerMatrix = 5000;
        private int fromWhatLevelRepeat = 40;
        private int fromStart = 38;
        private int toEnd = 49;
        
        private Dictionary<int, List<string>> wordsDictionary = new();
        private List<Vector2Int> emptyCells = new List<Vector2Int>();
        private char[,] currentMatrix;
        private Stack<Vector2Int> currentCells = new Stack<Vector2Int>();
        private LetterInfo[,] letterInfos;
        private int currentMatrixIndex = 0;
        
        public LevelGenerationService()
        {
            LoadAssets();
        }
        
        private void LoadAssets()
        {
            wordDistributionSO = AssetDatabase.LoadAssetAtPath<WordDistributionSO>("Assets/_Project/Prefabs/SO/WordDistribution/WordDistribution.asset");
            if (wordDistributionSO == null)
                Debug.LogError("WordDistributionSO not found at: Assets/_Project/Prefabs/SO/WordDistribution/WordDistribution.asset");

            levelsData = AssetDatabase.LoadAssetAtPath<LevelsData>("Assets/_Project/Prefabs/SO/tables/LevelsData.asset");
            if (levelsData == null)
                Debug.LogError("LevelsData not found at: Assets/_Project/Prefabs/SO/tables/LevelsData.asset");

            wordsData = AssetDatabase.LoadAssetAtPath<WordsData>("Assets/_Project/Prefabs/SO/tables/WordsData.asset");
            if (wordsData == null)
                Debug.LogError("WordsData not found at: Assets/_Project/Prefabs/SO/tables/WordsData.asset");

            levelsConfig = AssetDatabase.LoadAssetAtPath<LevelsConfigSO>("Assets/_Project/Prefabs/SO/LevelsConfig/LevelsConfig.asset");
            if (levelsConfig == null)
                Debug.LogError("LevelsConfigSO not found at: Assets/_Project/Prefabs/SO/LevelsConfig/LevelsConfig.asset");

            badWords = AssetDatabase.LoadAssetAtPath<BannedData>("Assets/_Project/Prefabs/SO/tables/BannedData.asset");
            if (badWords == null)
                Debug.LogError("BannedData not found at: Assets/_Project/Prefabs/SO/tables/BannedData.asset");
        }
        
        public async UniTask<GenerationResult> CreateWordDistributionAsync(IProgress<GenerationProgress> progress = null)
        {
            var result = new GenerationResult();

            try
            {
                // Check required assets
                if (wordsData == null)
                {
                    result.IsSuccess = false;
                    result.Errors.Add("WordsData asset not found. Please check the asset path.");
                    return result;
                }

                if (wordDistributionSO == null)
                {
                    result.IsSuccess = false;
                    result.Errors.Add("WordDistributionSO asset not found. Please check the asset path.");
                    return result;
                }

                if (levelsData == null)
                {
                    result.IsSuccess = false;
                    result.Errors.Add("LevelsData asset not found. Please check the asset path.");
                    return result;
                }

                progress?.Report(new GenerationProgress("Initializing word dictionary", 0, 1));

                wordsDictionary = new();
                foreach (WordsSet set in wordsData.WordsSets)
                    wordsDictionary.Add(set.LettersCount, set.WordsArray.ToList());

                wordDistributionSO.levelWordsList.Clear();
                
                int totalLevels = levelsData.Levels.Count;
                
                // Process base levels
                for (int i = 0; i < levelsData.Levels.Count; i++)
                {
                    var level = levelsData.Levels[i];
                    progress?.Report(new GenerationProgress($"Processing level {level.LevelNum}", i, totalLevels, level.LevelNum));
                    
                    List<string> levelWords = new();
                    int wordsCount = level.RowsCount * level.ColumnsCount / level.LettersCount;

                    for (int j = 0; j < wordsCount; j++)
                    {
                        if (wordsDictionary[level.LettersCount].Count > 0)
                        {
                            levelWords.Add(wordsDictionary[level.LettersCount][0]);
                            wordsDictionary[level.LettersCount].RemoveAt(0);
                        }
                    }

                    wordDistributionSO.levelWordsList.Add(new WordDistributionSO.LevelWords
                    {
                        levelIndex = level.LevelNum,
                        words = levelWords
                    });
                    
                    await UniTask.Delay(1);
                }
                
                // Process extended levels
                int levelNum = levelsData.Levels[^1].LevelNum;
                bool enoughWords = true;
                int extendedCount = 0;
                
                while (enoughWords)
                {
                    levelNum++;
                    extendedCount++;
                    int refNum = GetReferenceIndex(levelNum, fromStart, toEnd);
                    Level levelRef = levelsData.Levels[refNum];
                    int lettersCount = levelRef.LettersCount;
                    int wordsCountNew = levelRef.RowsCount * levelRef.ColumnsCount / lettersCount;
                    
                    if (wordsDictionary[lettersCount].Count < wordsCountNew)
                    {
                        result.Warnings.Add($"Not enough words with {lettersCount} letters for level {levelNum}!");
                        enoughWords = false;
                        break;
                    }
                    
                    progress?.Report(new GenerationProgress($"Processing extended level {levelNum}", totalLevels + extendedCount, totalLevels + extendedCount + 1, levelNum));
                    
                    List<string> levelWords = new();
                    for (int i = 0; i < wordsCountNew; i++)
                    {
                        levelWords.Add(wordsDictionary[lettersCount][0]);
                        wordsDictionary[lettersCount].RemoveAt(0);
                    }
                    
                    wordDistributionSO.levelWordsList.Add(new WordDistributionSO.LevelWords
                    {
                        levelIndex = levelNum,
                        words = levelWords
                    });
                    
                    await UniTask.Delay(1);
                }

                EditorUtility.SetDirty(wordDistributionSO);
                AssetDatabase.SaveAssets();
                
                result.Message = $"Word distribution created successfully. Generated {wordDistributionSO.levelWordsList.Count} level configurations.";
                
                progress?.Report(new GenerationProgress("Word distribution completed", 1, 1));
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Errors.Add($"Failed to create word distribution: {ex.Message}");
                Debug.LogError($"Word distribution creation failed: {ex}");
            }
            
            return result;
        }
        
        public async UniTask<GenerationResult> GenerateAllLevelsAsync(IProgress<GenerationProgress> progress = null)
        {
            var result = new GenerationResult();

            try
            {
                progress?.Report(new GenerationProgress("Clearing levels folder", 0, 1));
                ClearLevelsFolder();

                int totalLevels = levelsData.Levels.Count + (wordDistributionSO.levelWordsList.Count - levelsData.Levels.Count);
                int processedLevels = 0;

                // Generate base levels
                foreach (Level level in levelsData.Levels)
                {
                    progress?.Report(new GenerationProgress($"Generating level {level.LevelNum}", processedLevels, totalLevels, level.LevelNum));

                    var levelResult = await GenerateAndSaveLevelAsync(level.LevelNum, level);
                    if (!levelResult.IsSuccess)
                    {
                        result.Errors.AddRange(levelResult.Errors);
                        result.Warnings.AddRange(levelResult.Warnings);
                    }

                    processedLevels++;
                    await UniTask.Delay(1);
                }

                // Generate extended levels
                int start = fromStart;
                int end = toEnd;
                int index = start;

                for (int i = levelsData.Levels.Count; i < wordDistributionSO.levelWordsList.Count; i++)
                {
                    Level refLevel = levelsData.Levels[index];
                    int levelNum = i + 2;

                    progress?.Report(new GenerationProgress($"Generating extended level {levelNum}", processedLevels, totalLevels, levelNum));

                    var levelResult = await GenerateAndSaveLevelAsync(levelNum, refLevel);
                    if (!levelResult.IsSuccess)
                    {
                        result.Errors.AddRange(levelResult.Errors);
                        result.Warnings.AddRange(levelResult.Warnings);
                    }

                    index = ++index > end ? start : index;
                    processedLevels++;
                    await UniTask.Delay(1);
                }

                LoadLevelsFromFolder();
                AssetDatabase.SaveAssets();

                result.GeneratedLevelsCount = processedLevels;
                result.Message = $"Generated {processedLevels} levels successfully.";

                progress?.Report(new GenerationProgress("Generation completed", totalLevels, totalLevels));
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Errors.Add($"Failed to generate levels: {ex.Message}");
                Debug.LogError($"Level generation failed: {ex}");
            }

            return result;
        }

        public async UniTask<GenerationResult> GenerateOneLevelAsync(int levelNumber, IProgress<GenerationProgress> progress = null)
        {
            var result = new GenerationResult();

            try
            {
                progress?.Report(new GenerationProgress($"Generating level {levelNumber}", 0, 1, levelNumber));

                Level refLevel;
                if (levelNumber >= levelsData.Levels.Count)
                {
                    refLevel = levelsData.Levels[GetReferenceIndex(levelNumber, fromStart, toEnd)];
                }
                else
                {
                    refLevel = levelsData.Levels[levelNumber - 2];
                }

                var levelResult = await GenerateAndSaveLevelAsync(levelNumber, refLevel);
                result.IsSuccess = levelResult.IsSuccess;
                result.Errors = levelResult.Errors;
                result.Warnings = levelResult.Warnings;
                result.GeneratedLevelsCount = levelResult.IsSuccess ? 1 : 0;

                if (levelResult.IsSuccess)
                {
                    LoadLevelsFromFolder();
                    AssetDatabase.SaveAssets();
                    result.Message = $"Level {levelNumber} generated successfully.";
                }

                progress?.Report(new GenerationProgress("Generation completed", 1, 1, levelNumber));
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Errors.Add($"Failed to generate level {levelNumber}: {ex.Message}");
                Debug.LogError($"Level {levelNumber} generation failed: {ex}");
            }

            return result;
        }

        private int GetReferenceIndex(int currentLevel, int fromStart, int toEnd)
        {
            int lastLevelNum = levelsData.Levels[^1].LevelNum;
            int length = toEnd - fromStart + 1;
            return fromStart + ((currentLevel - lastLevelNum - 1) % length);
        }

        private async UniTask<GenerationResult> GenerateAndSaveLevelAsync(int levelNum, Level baseLevel)
        {
            var result = new GenerationResult();

            try
            {
                LevelsSO levelSO = ScriptableObject.CreateInstance<LevelsSO>();
                levelSO.FillFromLevel(baseLevel);
                levelSO.LevelNum = levelNum;

                var levelWordsData = wordDistributionSO.levelWordsList.Find(lw => lw.levelIndex == levelNum);
                if (levelWordsData == null)
                {
                    result.IsSuccess = false;
                    result.Errors.Add($"No word data found for level {levelNum}");
                    return result;
                }

                levelSO.Words = levelWordsData.words;

                var generationResult = await GenerateLevelNTimesAsync(levelSO, generateCount);
                result.Errors.AddRange(generationResult.Errors);
                result.Warnings.AddRange(generationResult.Warnings);

                string folderPath = LEVELS_FOLDER_PATH;
                AssetDatabase.CreateFolder("Assets/_Project/Prefabs/SO/", "Levels");
                string assetPath = $"{folderPath}/Level{levelSO.LevelNum}.asset";
                AssetDatabase.CreateAsset(levelSO, assetPath);

                result.Message = $"Level {levelNum} generated with {levelSO.FillMatrixes?.Count ?? 0} matrices";
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Errors.Add($"Failed to generate and save level {levelNum}: {ex.Message}");
            }

            return result;
        }

        private void ClearLevelsFolder()
        {
            string folderPath = LEVELS_FOLDER_PATH;
            if (!AssetDatabase.IsValidFolder(folderPath))
                return;

            string[] assetGUIDs = AssetDatabase.FindAssets("", new[] { folderPath });

            foreach (string guid in assetGUIDs)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                if (path.EndsWith(".asset"))
                {
                    AssetDatabase.DeleteAsset(path);
                }
            }

            AssetDatabase.Refresh();
        }

        private void LoadLevelsFromFolder()
        {
            if (levelsConfig == null)
            {
                Debug.LogError("LevelsConfigSO is null. Cannot load levels from folder.");
                return;
            }

            levelsConfig.Levels.Clear();
            string folderPath = LEVELS_FOLDER_PATH;

            string[] guids = AssetDatabase.FindAssets("t:LevelsSO", new[] { folderPath });
            List<LevelsSO> foundLevels = guids
                .Select(guid => AssetDatabase.LoadAssetAtPath<LevelsSO>(AssetDatabase.GUIDToAssetPath(guid)))
                .Where(level => level != null)
                .ToList();

            levelsConfig.Levels = foundLevels;

            int count = levelsConfig.Levels.Count - (fromWhatLevelRepeat - 2);
            levelsConfig.LastLevels = levelsConfig.Levels.TakeLast(count).ToList();

            EditorUtility.SetDirty(levelsConfig);
        }
    }
}

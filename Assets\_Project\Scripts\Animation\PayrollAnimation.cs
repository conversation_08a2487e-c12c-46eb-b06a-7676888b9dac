using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class PayrollAnimation : MonoBehaviour
{
    [Header("Coin Animation:")]
    [SerializeField] private Canvas _uiCanvas;
    [SerializeField] private Transform _coinsParent;
    [SerializeField] private GameObject _coinPrefab;
    [SerializeField] private RectTransform _targetPosition;
    [SerializeField] private RectTransform _defaultStartPosition;

    [Header("Value Panel:")]
    [SerializeField] private TextMeshProUGUI _valueText;
    [SerializeField] private RectTransform _scalableObject;

    [Header("Animation Settings:")]
    [SerializeField] private int _coinCount = 8;
    [SerializeField] private float _explosionRadius = 150f;
    [SerializeField] private float _explosionDuration = 0.5f;
    [SerializeField] private float _flyDuration = 1f;
    [SerializeField] private float _delayBetweenCoins = 0.08f;
    [SerializeField] private Vector2 _coinSize = new(60, 60);
    [SerializeField] private float _coinScale = 1f;

    [Header("Randomization:")]
    [SerializeField] private float _radiusVariation = 50f;
    [SerializeField] private float _angleVariation = 20f;
    [SerializeField] private float _speedVariation = 0.3f;

    private int _addAmount;
    private bool _currencyAnimationStarted;
    private bool _isAnimating = false;

    public static PayrollAnimation Instance { get; private set; }

    public event Action<int> OnComplete;

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    public void RunCurrencyAnimation(RectTransform fromPosition, int amount)
    {
        if (_isAnimating) return;

        _addAmount = amount;
        _currencyAnimationStarted = false;
        _isAnimating = true;
        StartCoroutine(PlayCoinAnimation(fromPosition));
    }

    private IEnumerator PlayCoinAnimation(RectTransform fromPosition)
    {
        if (SoundPlayer.Instance != null)
            SoundPlayer.Instance.PlaySound(SoundActionType.AddWord);

        RectTransform startPosition = fromPosition != null ? fromPosition : _defaultStartPosition;
        yield return StartCoroutine(AnimateCoins(startPosition));
    }

    private IEnumerator AnimateCoins(RectTransform startPosition)
    {
        var coins = new List<GameObject>();

        for (int i = 0; i < _coinCount; i++)
        {
            GameObject coin = Instantiate(_coinPrefab);

            if (coin.TryGetComponent<RectTransform>(out var coinRect))
            {
                Transform parent = _coinsParent != null ? _coinsParent : _uiCanvas.transform;
                coinRect.SetParent(parent, false);

                coinRect.localPosition = Vector3.zero;
                coinRect.localRotation = Quaternion.identity;
                coinRect.localScale = Vector3.one * _coinScale;

                coinRect.anchorMin = new Vector2(0.5f, 0.5f);
                coinRect.anchorMax = new Vector2(0.5f, 0.5f);
                coinRect.pivot = new Vector2(0.5f, 0.5f);

                coinRect.sizeDelta = _coinSize;
                coinRect.anchoredPosition = startPosition.anchoredPosition;
            }

            coins.Add(coin);
        }

        yield return StartCoroutine(ExplodeCoins(coins, startPosition));
        yield return StartCoroutine(FlyCoinsToTarget(coins));
    }

    private IEnumerator AnimateCurrencyIncrease()
    {
        int fromMoney = CurrencyManager.Balance;
        int toMoney = fromMoney + _addAmount;

        if (_valueText != null)
            _valueText.text = fromMoney.ToString();

        float duration = 1f;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.unscaledDeltaTime;
            float progress = elapsed / duration;

            int currentValue = fromMoney + Mathf.RoundToInt(_addAmount * progress);
            if (_valueText != null)
                _valueText.text = currentValue.ToString();

            if (_scalableObject != null && elapsed < duration)
            {
                float scaleProgress = Mathf.PingPong(elapsed * 4f, 1f);
                _scalableObject.localScale = Vector3.one * (1f + scaleProgress * 0.2f);
            }

            yield return null;
        }

        if (_scalableObject != null)
        {
            _scalableObject.DOScale(1.6f, 0.2f).OnComplete(() => {
                _scalableObject.DOScale(1f, 0.2f);
            });
        }

        OnComplete?.Invoke(_addAmount);
        _isAnimating = false;
    }





    private IEnumerator ExplodeCoins(List<GameObject> coins, RectTransform startPosition)
    {
        for (int i = 0; i < coins.Count; i++)
        {
            var coin = coins[i];
            if (coin != null && coin.TryGetComponent<RectTransform>(out var coinRect))
            {
                float baseAngle = 360f / coins.Count * i;
                float randomOffset = UnityEngine.Random.Range(-_angleVariation, _angleVariation);
                float angle = baseAngle + randomOffset;

                Vector2 direction = new(Mathf.Cos(angle * Mathf.Deg2Rad), Mathf.Sin(angle * Mathf.Deg2Rad));

                float randomRadius = _explosionRadius + UnityEngine.Random.Range(-_radiusVariation, _radiusVariation);
                Vector2 targetPos = startPosition.anchoredPosition + direction * randomRadius;

                float randomDuration = _explosionDuration + UnityEngine.Random.Range(-_speedVariation, _speedVariation);

                coinRect.DOAnchorPos(targetPos, randomDuration)
                    .SetEase(Ease.OutQuad);

                coin.transform.DOScale(1.2f, randomDuration * 0.5f)
                    .SetLoops(2, LoopType.Yoyo)
                    .SetEase(Ease.OutQuad);
            }
        }

        yield return new WaitForSeconds(_explosionDuration + 0.2f);
    }

    private IEnumerator FlyCoinsToTarget(List<GameObject> coins)
    {
        Vector2 targetPos = GetTargetPosition(_targetPosition);

        for (int i = 0; i < coins.Count; i++)
        {
            var coin = coins[i];
            if (coin != null && coin.TryGetComponent<RectTransform>(out var coinRect))
            {
                float delay = i * _delayBetweenCoins;
                float randomFlyDuration = _flyDuration + UnityEngine.Random.Range(-_speedVariation, _speedVariation);

                GameObject coinToDestroy = coin;

                coinRect.DOAnchorPos(targetPos, randomFlyDuration)
                    .SetEase(Ease.InQuad)
                    .SetDelay(delay)
                    .OnComplete(() => OnCoinReachedTarget(coinToDestroy));

                coin.transform.DOScale(0.3f, randomFlyDuration)
                    .SetEase(Ease.InQuad)
                    .SetDelay(delay);
            }
        }

        float maxDuration = _flyDuration + 0.3f + (coins.Count - 1) * _delayBetweenCoins;
        yield return new WaitForSeconds(maxDuration);
    }

    private void OnCoinReachedTarget(GameObject coin)
    {
        if (coin != null)
            Destroy(coin);

        if (!_currencyAnimationStarted)
        {
            _currencyAnimationStarted = true;

            if (_scalableObject != null)
            {
                _scalableObject.DOScale(1.4f, 0.15f)
                    .SetLoops(2, LoopType.Yoyo)
                    .SetEase(Ease.OutQuad);
            }

            CurrencyManager.AddWithoutAnimation(_addAmount);
            StartCoroutine(AnimateCurrencyIncrease());
        }
    }

    private Vector2 GetTargetPosition(RectTransform target)
    {
        if (target == null || _uiCanvas == null)
            return Vector2.zero;

        if (!_uiCanvas.TryGetComponent<RectTransform>(out var canvasRect))
            return Vector2.zero;

        return canvasRect.InverseTransformPoint(target.position);
    }
}

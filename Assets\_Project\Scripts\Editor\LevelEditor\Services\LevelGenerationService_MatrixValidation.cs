using System.Collections.Generic;
using UnityEngine;

namespace LevelEditor.Services
{
    public partial class LevelGenerationService
    {
        private bool IsMatrixValid(char[,] matrix, LevelsSO level, out string failReason, out string failWord)
        {
            failReason = "";
            failWord = "";

            // Check matrix uniqueness (if enabled)
            if (LevelGenerationService.EnableMatrixUniquenessCheck)
            {
                if (EqualToMatrixes(matrix, level.FillMatrixes))
                {
                    failReason = "Matrix is identical to existing matrix";
                    return false;
                }
            }

            // Check bad words (if enabled)
            if (LevelGenerationService.EnableBadWordCheck)
            {
                if (!LevelsPregeneratorExtension.ValidateMatrixForBadWords(matrix, badWords.Words))
                {
                    failReason = "Matrix contains bad words";
                    return false;
                }
            }

            // Check matrix integrity (if enabled)
            if (LevelGenerationService.EnableMatrixIntegrityCheck)
            {
                foreach (string word in level.Words)
                {
                    if (HasAmbiguousPath(matrix, word))
                    {
                        failReason = $"Word has multiple paths";
                        failWord = word;
                        return false;
                    }
                }
            }

            return true;
        }

        private bool EqualToMatrixes(char[,] matrix, List<FillMatrix> other)
        {
            foreach (FillMatrix otherMatrix in other)
            {
                if (AreMatricesEqual(matrix, otherMatrix))
                {
                    return true;
                }
            }
            return false;
        }
        
        private bool AreMatricesEqual(char[,] a, FillMatrix b)
        {
            if (a.GetLength(0) != b.RowCount || a.GetLength(1) != b.ColCount) return false;

            for (int row = 0; row < a.GetLength(0); row++)
                for (int col = 0; col < a.GetLength(1); col++)
                {
                    char la = a[row, col];
                    char lb = b[row, col].GetCharLetter();
                    if (la != lb) return false;
                }

            return true;
        }
        
        private bool IsPalindrome(string word)
        {
            int len = word.Length;
            for (int i = 0; i < len / 2; i++)
            {
                if (word[i] != word[len - 1 - i])
                    return false;
            }
            return true;
        }
        
        private bool HasAmbiguousPath(char[,] matrix, string word)
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            int totalPaths = 0;

            for (int r = 0; r < rows; r++)
            {
                for (int c = 0; c < cols; c++)
                {
                    if (matrix[r, c] != word[0])
                        continue;

                    bool[,] visited = new bool[rows, cols];
                    List<Vector2Int> path = new List<Vector2Int> { new Vector2Int(r, c) };

                    CountPaths(matrix, word, r, c, 0, visited, ref totalPaths, path);
                    bool isPalindrome = IsPalindrome(word);
                    if (totalPaths >= 2 && !isPalindrome || totalPaths >= 3 && isPalindrome)
                    {
                        return true;
                    }
                }
            }

            return false;
        }
        
        private void CountPaths(char[,] matrix, string word, int r, int c, int index, bool[,] visited, ref int totalPaths, List<Vector2Int> path)
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);

            if (matrix[r, c] != word[index])
                return;

            if (index == word.Length - 1)
            {
                totalPaths++;
                return;
            }

            visited[r, c] = true;

            foreach (var dir in Directions.directions)
            {
                int nr = r + dir.x;
                int nc = c + dir.y;

                if (nr < 0 || nr >= rows || nc < 0 || nc >= cols || visited[nr, nc])
                    continue;

                if (matrix[nr, nc] == word[index + 1])
                {
                    path.Add(new Vector2Int(nr, nc));
                    CountPaths(matrix, word, nr, nc, index + 1, visited, ref totalPaths, path);
                    path.RemoveAt(path.Count - 1);

                    if (totalPaths >= 2)
                    {
                        visited[r, c] = false;
                        return;
                    }
                }
            }

            visited[r, c] = false;
        }

        private bool IsCellEmpty(int r, int c)
        {
            return currentMatrix[r, c] == '0';
        }
        
        private bool IsCellEmpty(Vector2Int pos)
        {
            return IsCellEmpty(pos.x, pos.y);
        }
        
        private bool IsCellInside(int r, int c)
        {
            int rows = currentMatrix.GetLength(0);
            int cols = currentMatrix.GetLength(1);
            return r >= 0 && r < rows && c >= 0 && c < cols;
        }
        
        private bool IsCellInside(Vector2Int pos)
        {
            return IsCellInside(pos.x, pos.y);
        }
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Cysharp.Threading.Tasks;

namespace LevelEditor.Services
{
    public class MatrixIntegrityValidationService : BaseValidationService
    {
        public override string ServiceName => "Matrix Integrity Validation";
        
        protected override async UniTask ValidateInternalAsync(LevelsSO level, ValidationResult result, IProgress<ValidationProgress> progress)
        {
            if (level.FillMatrixes == null || level.FillMatrixes.Count == 0)
            {
                result.IsValid = false;
                result.Errors.Add(new ValidationError("Data", "Level has no matrices to validate"));
                return;
            }
            
            ReportProgress(progress, "Starting matrix integrity validation", 0, level.FillMatrixes.Count);
            
            for (int matrixIndex = 0; matrixIndex < level.FillMatrixes.Count; matrixIndex++)
            {
                ReportProgress(progress, $"Checking matrix {matrixIndex + 1}", matrixIndex, level.FillMatrixes.Count);
                
                var matrix = level.FillMatrixes[matrixIndex];
                
                // Check matrix dimensions
                if (matrix.RowCount != level.RowsCount || matrix.ColCount != level.ColumnsCount)
                {
                    result.IsValid = false;
                    result.Errors.Add(new ValidationError(
                        "Dimensions", 
                        $"Matrix {matrixIndex} has incorrect dimensions",
                        matrixIndex,
                        $"Expected: {level.RowsCount}x{level.ColumnsCount}, Actual: {matrix.RowCount}x{matrix.ColCount}"
                    ));
                }
                
                // Check for empty cells
                bool hasEmptyCells = false;
                for (int r = 0; r < matrix.RowCount; r++)
                {
                    for (int c = 0; c < matrix.ColCount; c++)
                    {
                        var letterData = matrix[r, c];
                        if (string.IsNullOrEmpty(letterData.Letter) || letterData.Letter == "0")
                        {
                            hasEmptyCells = true;
                            break;
                        }
                    }
                    if (hasEmptyCells) break;
                }
                
                if (hasEmptyCells)
                {
                    result.IsValid = false;
                    result.Errors.Add(new ValidationError(
                        "EmptyCells", 
                        $"Matrix {matrixIndex} contains empty cells",
                        matrixIndex
                    ));
                }
                
                // Check word integrity
                await ValidateWordsInMatrix(level, matrix, matrixIndex, result);
                
                await DelayForUI();
            }
            
            ReportProgress(progress, "Matrix integrity validation completed", level.FillMatrixes.Count, level.FillMatrixes.Count);
        }
        
        private async UniTask ValidateWordsInMatrix(LevelsSO level, FillMatrix matrix, int matrixIndex, ValidationResult result)
        {
            foreach (string word in level.Words)
            {
                bool wordFound = false;

                // Search for word in matrix
                for (int r = 0; r < matrix.RowCount && !wordFound; r++)
                {
                    for (int c = 0; c < matrix.ColCount && !wordFound; c++)
                    {
                        if (matrix[r, c].GetCharLetter() == word[0])
                        {
                            if (FindWordFromPosition(matrix, word, r, c))
                            {
                                wordFound = true;
                                // Debug info for successful finds
                                Debug.Log($"Found word '{word}' in matrix {matrixIndex} starting at ({r},{c})");
                            }
                        }
                    }
                }

                if (!wordFound)
                {
                    result.IsValid = false;
                    result.Errors.Add(new ValidationError(
                        "MissingWord",
                        $"Word '{word}' not found in matrix {matrixIndex}",
                        matrixIndex
                    ));

                    // Debug info for missing words
                    Debug.LogWarning($"Could not find word '{word}' in matrix {matrixIndex}");
                    LogMatrixContent(matrix, matrixIndex);
                }

                await UniTask.Yield();
            }
        }

        private void LogMatrixContent(FillMatrix matrix, int matrixIndex)
        {
            Debug.Log($"Matrix {matrixIndex} content:");
            for (int r = 0; r < matrix.RowCount; r++)
            {
                string row = "";
                for (int c = 0; c < matrix.ColCount; c++)
                {
                    row += matrix[r, c].GetCharLetter() + " ";
                }
                Debug.Log($"Row {r}: {row}");
            }
        }
        
        private bool FindWordFromPosition(FillMatrix matrix, string word, int startR, int startC)
        {
            // Use snake-like search - can move to any adjacent cell (4 directions) but can't reuse cells
            bool[,] visited = new bool[matrix.RowCount, matrix.ColCount];
            return FindWordRecursive(matrix, word, startR, startC, 0, visited);
        }

        private bool FindWordRecursive(FillMatrix matrix, string word, int r, int c, int letterIndex, bool[,] visited)
        {
            // Check bounds
            if (r < 0 || r >= matrix.RowCount || c < 0 || c >= matrix.ColCount)
                return false;

            // Check if cell is already visited
            if (visited[r, c])
                return false;

            // Check if current letter matches
            if (matrix[r, c].GetCharLetter() != word[letterIndex])
                return false;

            // If we found the complete word
            if (letterIndex == word.Length - 1)
                return true;

            // Mark current cell as visited
            visited[r, c] = true;

            // Try all 4 adjacent directions (no diagonals)
            var directions = new Vector2Int[]
            {
                new Vector2Int(0, 1),   // Right
                new Vector2Int(1, 0),   // Down
                new Vector2Int(0, -1),  // Left
                new Vector2Int(-1, 0)   // Up
            };

            foreach (var direction in directions)
            {
                int newR = r + direction.x;
                int newC = c + direction.y;

                if (FindWordRecursive(matrix, word, newR, newC, letterIndex + 1, visited))
                {
                    visited[r, c] = false; // Backtrack
                    return true;
                }
            }

            // Backtrack - unmark current cell
            visited[r, c] = false;
            return false;
        }
        
        protected override string CreateSummary(ValidationResult result)
        {
            if (result.IsValid)
            {
                return $"{ServiceName}: All matrices are valid";
            }
            else
            {
                var errorGroups = result.Errors.GroupBy(e => e.ErrorType);
                var summary = $"{ServiceName}: ";
                var parts = new List<string>();
                
                foreach (var group in errorGroups)
                {
                    parts.Add($"{group.Count()} {group.Key.ToLower()} error(s)");
                }
                
                return summary + string.Join(", ", parts);
            }
        }
    }
}

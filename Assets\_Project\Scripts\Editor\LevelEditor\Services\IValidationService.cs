using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;

namespace LevelEditor.Services
{
    public interface IValidationService
    {
        string ServiceName { get; }
        UniTask<ValidationResult> ValidateAsync(LevelsSO level, IProgress<ValidationProgress> progress = null);
    }

    [Serializable]
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ServiceName { get; set; }
        public List<ValidationError> Errors { get; set; } = new List<ValidationError>();
        public string Summary { get; set; }
        
        public ValidationResult(string serviceName, bool isValid = true)
        {
            ServiceName = serviceName;
            IsValid = isValid;
        }
    }

    [Serializable]
    public class ValidationError
    {
        public string ErrorType { get; set; }
        public string Description { get; set; }
        public int MatrixIndex { get; set; }
        public string AdditionalInfo { get; set; }
        
        public ValidationError(string errorType, string description, int matrixIndex = -1, string additionalInfo = "")
        {
            ErrorType = errorType;
            Description = description;
            MatrixIndex = matrixIndex;
            AdditionalInfo = additionalInfo;
        }
    }

    [Serializable]
    public class ValidationProgress
    {
        public string ServiceName { get; set; }
        public string CurrentOperation { get; set; }
        public int ProcessedItems { get; set; }
        public int TotalItems { get; set; }
        public float Progress => TotalItems > 0 ? (float)ProcessedItems / TotalItems : 0f;
        
        public ValidationProgress(string serviceName, string operation, int processed, int total)
        {
            ServiceName = serviceName;
            CurrentOperation = operation;
            ProcessedItems = processed;
            TotalItems = total;
        }
    }
}

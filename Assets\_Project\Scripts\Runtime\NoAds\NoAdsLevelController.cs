using UnityEngine;
using VG;

public class NoAdsLevelController : MonoBehaviour
{
    [SerializeField] private HintsConfig _hintsConfig;
    [SerializeField] private NoAdsButton _noAdsButton;
    
    private void Start()
    {
        LevelManager.onStart += CheckNoAdsButtonVisibility;
        CheckNoAdsButtonVisibility();
    }
    
    private void OnDestroy()
    {
        LevelManager.onStart -= CheckNoAdsButtonVisibility;
    }
    
    private void CheckNoAdsButtonVisibility()
    {
        if (_hintsConfig == null || _noAdsButton == null)
            return;
            
        // Проверяем, куплены ли уже NoAds
        if (PurchasesHandler.ProductPurchased(Key_Product.no_ads))
        {
            _noAdsButton.gameObject.SetActive(false);
            return;
        }
        
        // Получаем текущий уровень (с учетом того, что туториал не считается)
        int currentLevel = GetEffectiveLevel();
        
        // Показываем кнопку только если уровень >= CountToShowAds
        bool shouldShow = currentLevel >= _hintsConfig.CountToShowAds;
        _noAdsButton.gameObject.SetActive(shouldShow);
        
        Debug.Log($"NoAds button visibility: Level {currentLevel}, Required {_hintsConfig.CountToShowAds}, Show: {shouldShow}");
    }
    
    private int GetEffectiveLevel()
    {
        // Если туториал не пройден, считаем что уровень 0
        if (!LevelManager.Instance.HasTutorialCompleted)
            return 0;
            
        // Иначе возвращаем текущий уровень
        return LevelManager.CurrentLevel + 1; // +1 потому что CurrentLevel начинается с 0
    }
}

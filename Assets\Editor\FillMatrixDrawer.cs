using UnityEditor;
using UnityEngine;

[CustomPropertyDrawer(typeof(FillMatrix))]
public class FillMatrixDrawer : PropertyDrawer
{
    private const float CellSize = 20f;
    private const float CellSpacing = 4f;

    public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
    {
        SerializedProperty rowsProp = property.FindPropertyRelative("rows");
        int rowCount = rowsProp.arraySize;
        return (CellSize + CellSpacing) * rowCount + EditorGUIUtility.singleLineHeight;
    }

    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        EditorGUI.LabelField(
            new Rect(position.x, position.y, position.width, EditorGUIUtility.singleLineHeight),
            label
        );

        SerializedProperty rowsProp = property.FindPropertyRelative("rows");

        position.y += EditorGUIUtility.singleLineHeight + CellSpacing;

        int rowCount = rowsProp.arraySize;
        int colCount = rowCount > 0
            ? rowsProp.GetArrayElementAtIndex(0).FindPropertyRelative("row").arraySize
            : 0;

        for (int row = 0; row < rowCount; row++)
        {
            SerializedProperty rowProp = rowsProp.GetArrayElementAtIndex(row).FindPropertyRelative("row");

            for (int col = 0; col < colCount; col++)
            {
                SerializedProperty letterDataProp = rowProp.GetArrayElementAtIndex(col);

                SerializedProperty letterProp = letterDataProp.FindPropertyRelative("Letter");
                SerializedProperty wordProp = letterDataProp.FindPropertyRelative("Word");
                SerializedProperty indexProp = letterDataProp.FindPropertyRelative("IndexInWord");

                string display = letterProp.stringValue;
                string tooltip = $"{wordProp.stringValue} [{indexProp.intValue}]";

                if (string.IsNullOrEmpty(display)) display = " ";

                Rect cellRect = new Rect(
                    position.x + col * (CellSize + CellSpacing),
                    position.y + row * (CellSize + CellSpacing),
                    CellSize,
                    CellSize
                );

                int hash = string.IsNullOrEmpty(wordProp.stringValue) ? 0 : wordProp.stringValue.GetHashCode();
                Color bgColor = Color.gray;
                EditorGUI.DrawRect(cellRect, bgColor);

                GUIContent content = new GUIContent(display, tooltip);
                GUIStyle style = new GUIStyle(GUI.skin.label)
                {
                    alignment = TextAnchor.MiddleCenter,
                    fontStyle = FontStyle.Bold
                };

                EditorGUI.LabelField(cellRect, content, style);
            }
        }
    }
}
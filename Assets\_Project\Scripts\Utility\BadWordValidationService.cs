using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

[Serializable]
public class BadWordMatch
{
    public string word;
    public Vector2Int startPosition;
    public Vector2Int endPosition;
    public string direction;
    public List<Vector2Int> path;

    public BadWordMatch(string word, Vector2Int startPos, Vector2Int endPos, string direction, List<Vector2Int> path = null)
    {
        this.word = word;
        this.startPosition = startPos;
        this.endPosition = endPos;
        this.direction = direction;
        this.path = path ?? new List<Vector2Int>();
    }
}

public static class BadWordValidationService
{
    private static readonly Vector2Int[] AllDirections = new Vector2Int[]
    {
        new Vector2Int(0, 1),
        new Vector2Int(1, 0),
        new Vector2Int(0, -1),
        new Vector2Int(-1, 0),
        new Vector2Int(1, 1),
        new Vector2Int(1, -1),
        new Vector2Int(-1, 1),
        new Vector2Int(-1, -1)
    };

    public static bool HasBadWords(char[,] matrix, List<string> bannedWords)
    {
        if (bannedWords == null || bannedWords.Count == 0)
            return false;

        HashSet<string> badWordsSet = new HashSet<string>(bannedWords);
        int rows = matrix.GetLength(0);
        int cols = matrix.GetLength(1);

        foreach (Vector2Int direction in AllDirections)
        {
            for (int startRow = 0; startRow < rows; startRow++)
            {
                for (int startCol = 0; startCol < cols; startCol++)
                {
                    if (CheckDirectionForBadWords(matrix, new Vector2Int(startRow, startCol), direction, badWordsSet))
                    {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public static bool HasBadWords(FillMatrix matrix, List<string> bannedWords)
    {
        if (bannedWords == null || bannedWords.Count == 0)
            return false;

        char[,] charMatrix = ConvertToCharMatrix(matrix);
        return HasBadWords(charMatrix, bannedWords);
    }

    public static List<BadWordMatch> FindAllBadWords(char[,] matrix, List<string> bannedWords)
    {
        List<BadWordMatch> matches = new List<BadWordMatch>();
        
        if (bannedWords == null || bannedWords.Count == 0)
            return matches;

        HashSet<string> badWordsSet = new HashSet<string>(bannedWords);
        int rows = matrix.GetLength(0);
        int cols = matrix.GetLength(1);

        foreach (Vector2Int direction in AllDirections)
        {
            for (int startRow = 0; startRow < rows; startRow++)
            {
                for (int startCol = 0; startCol < cols; startCol++)
                {
                    var foundMatches = FindBadWordsInDirection(matrix, new Vector2Int(startRow, startCol), direction, badWordsSet);
                    matches.AddRange(foundMatches);
                }
            }
        }

        return RemoveDuplicates(matches);
    }

    public static List<BadWordMatch> FindAllBadWords(FillMatrix matrix, List<string> bannedWords)
    {
        char[,] charMatrix = ConvertToCharMatrix(matrix);
        return FindAllBadWords(charMatrix, bannedWords);
    }

    private static bool CheckDirectionForBadWords(char[,] matrix, Vector2Int startPos, Vector2Int direction, HashSet<string> badWords)
    {
        int rows = matrix.GetLength(0);
        int cols = matrix.GetLength(1);
        
        string currentWord = "";
        Vector2Int currentPos = startPos;

        for (int i = 0; i < 8 && IsValidPosition(currentPos, rows, cols); i++)
        {
            currentWord += matrix[currentPos.x, currentPos.y];

            if (currentWord.Length >= 3 && badWords.Contains(currentWord))
            {
                return true;
            }

            currentPos += direction;
        }

        return false;
    }

    private static List<BadWordMatch> FindBadWordsInDirection(char[,] matrix, Vector2Int startPos, Vector2Int direction, HashSet<string> badWords)
    {
        List<BadWordMatch> matches = new List<BadWordMatch>();
        int rows = matrix.GetLength(0);
        int cols = matrix.GetLength(1);
        
        string currentWord = "";
        Vector2Int currentPos = startPos;
        List<Vector2Int> path = new List<Vector2Int>();

        for (int i = 0; i < 8 && IsValidPosition(currentPos, rows, cols); i++)
        {
            currentWord += matrix[currentPos.x, currentPos.y];
            path.Add(currentPos);

            if (currentWord.Length >= 3 && badWords.Contains(currentWord))
            {
                Vector2Int endPos = currentPos;
                string directionName = GetDirectionName(direction);
                
                BadWordMatch match = new BadWordMatch(currentWord, startPos, endPos, directionName, new List<Vector2Int>(path));
                matches.Add(match);
            }

            currentPos += direction;
        }

        return matches;
    }

    private static char[,] ConvertToCharMatrix(FillMatrix matrix)
    {
        int rows = matrix.RowCount;
        int cols = matrix.ColCount;
        char[,] charMatrix = new char[rows, cols];

        for (int i = 0; i < rows; i++)
        {
            for (int j = 0; j < cols; j++)
            {
                charMatrix[i, j] = matrix[i, j].GetCharLetter();
            }
        }

        return charMatrix;
    }

    private static List<BadWordMatch> RemoveDuplicates(List<BadWordMatch> matches)
    {
        return matches
            .GroupBy(m => new { m.word, m.startPosition, m.endPosition, m.direction })
            .Select(g => g.First())
            .ToList();
    }

    private static bool IsValidPosition(Vector2Int pos, int rows, int cols)
    {
        return pos.x >= 0 && pos.x < rows && pos.y >= 0 && pos.y < cols;
    }

    private static string GetDirectionName(Vector2Int direction)
    {
        if (direction == new Vector2Int(0, 1)) return "Right";
        if (direction == new Vector2Int(1, 0)) return "Down";
        if (direction == new Vector2Int(0, -1)) return "Left";
        if (direction == new Vector2Int(-1, 0)) return "Up";
        if (direction == new Vector2Int(1, 1)) return "Down-Right";
        if (direction == new Vector2Int(1, -1)) return "Down-Left";
        if (direction == new Vector2Int(-1, 1)) return "Up-Right";
        if (direction == new Vector2Int(-1, -1)) return "Up-Left";
        return "Unknown";
    }
}

using UnityEngine;
using VG;

public class LevelCompletionService : MonoBehaviour
{
    [SerializeField] private int _baseCompletionReward = 50;
    [SerializeField] private VictoryAnimator _victoryAnimator;
    [SerializeField] private BuyNoAdsAnimator _buyNoAdsAnimator;

    private int _adsCount = 0;
    private const int AdsUntilNoAdsPrompt = 5;
    private bool _isProcessing = false;

    public static LevelCompletionService Instance { get; private set; }

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else if (Instance != this)
        {
            Destroy(gameObject);
        }
    }

    private void OnEnable() => Ads.Interstitial.onShown += OnInterstitialShown;
    private void OnDisable() => Ads.Interstitial.onShown -= OnInterstitialShown;

    public void SkipLevel()
    { 
        CurrencyManager.Add(_baseCompletionReward);
        LoadNextLevel();

        if (!PurchasesHandler.ProductPurchased(Key_Product.no_ads))
            Ads.Interstitial.Show();
    }

    public void CompleteLevelWithRewardAd()
    {
        if (_isProcessing) return;
        _isProcessing = true;

        Ads.Rewarded.Show(onShown: OnRewardAdResult);
    }

    private void OnRewardAdResult(Ads.Rewarded.Result result)
    {
        switch (result)
        {
            case Ads.Rewarded.Result.Success:
                OnRewardSuccess();
                break;
            default:
                SkipLevel();
                break;
        }
    }

    private void OnRewardSuccess()
    {
        int rewardAmount = _baseCompletionReward * 2;
        CurrencyManager.Add(rewardAmount);
        LoadNextLevel();
    }

    private void OnInterstitialShown(string key_ad, Ads.Interstitial.Result result)
    {
        if (result == Ads.Interstitial.Result.Cooldown ||
            result == Ads.Interstitial.Result.NoAds) return;

        _adsCount++;
        if (_adsCount >= AdsUntilNoAdsPrompt)
        {
            _adsCount = 0;
            _buyNoAdsAnimator.Show();
        }
    }

    private void LoadNextLevel()
    {
        _victoryAnimator.Restore();
        LevelManager.LoadNextLevel();
        _isProcessing = false;
    }

    public int GetRewardAmount()
    {
        return _baseCompletionReward * 2;
    }

    public int GetBaseReward()
    {
        return _baseCompletionReward;
    }

    public void SetBaseCompletionReward(int newReward)
    {
        if (newReward > 0)
            _baseCompletionReward = newReward;
    }

    public bool IsRewardAdAvailable()
    {
        return true;
    }
}

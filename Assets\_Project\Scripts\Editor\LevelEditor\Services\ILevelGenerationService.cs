using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;

namespace LevelEditor.Services
{
    public interface ILevelGenerationService
    {
        UniTask<GenerationResult> GenerateAllLevelsAsync(IProgress<GenerationProgress> progress = null);
        UniTask<GenerationResult> GenerateOneLevelAsync(int levelNumber, IProgress<GenerationProgress> progress = null);
        UniTask<GenerationResult> CreateWordDistributionAsync(IProgress<GenerationProgress> progress = null);
    }

    [Serializable]
    public class GenerationResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public int GeneratedLevelsCount { get; set; }
        
        public GenerationResult(bool success = true, string message = "")
        {
            IsSuccess = success;
            Message = message;
        }
    }

    [Serializable]
    public class GenerationProgress
    {
        public string CurrentOperation { get; set; }
        public int ProcessedLevels { get; set; }
        public int TotalLevels { get; set; }
        public int CurrentLevelNumber { get; set; }
        public int CurrentMatrixAttempt { get; set; }
        public int MaxMatrixAttempts { get; set; }
        public float Progress => TotalLevels > 0 ? (float)ProcessedLevels / TotalLevels : 0f;
        
        public GenerationProgress(string operation, int processed, int total, int currentLevel = 0)
        {
            CurrentOperation = operation;
            ProcessedLevels = processed;
            TotalLevels = total;
            CurrentLevelNumber = currentLevel;
        }
    }
}

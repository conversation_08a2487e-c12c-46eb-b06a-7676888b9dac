using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class CurrencyAnimationExample : MonoBehaviour
{
    [Header("UI Elements")]
    [SerializeField] private Button _rewardButton;
    [SerializeField] private Button _levelCompleteButton;
    [SerializeField] private TextMeshProUGUI _balanceText;

    [Header("Settings")]
    [SerializeField] private int _rewardAmount = 50;
    [SerializeField] private int _levelReward = 100;

    private void Start()
    {
        CurrencyManager.OnBalanceChanged += OnBalanceChanged;

        if (_rewardButton != null)
            _rewardButton.onClick.AddListener(() => OnRewardClick(_rewardButton.GetComponent<RectTransform>(), _rewardAmount));

        if (_levelCompleteButton != null)
            _levelCompleteButton.onClick.AddListener(() => OnRewardClick(_levelCompleteButton.GetComponent<RectTransform>(), _levelReward));

        if (PayrollAnimation.Instance != null)
        {
            PayrollAnimation.Instance.OnComplete += OnAnimationComplete;
        }

        UpdateBalanceDisplay();
    }

    private void OnDestroy()
    {
        CurrencyManager.OnBalanceChanged -= OnBalanceChanged;

        if (PayrollAnimation.Instance != null)
        {
            PayrollAnimation.Instance.OnComplete -= OnAnimationComplete;
        }
    }

    private void OnRewardClick(RectTransform fromPosition, int amount)
    {
        // Анимация с разлетающимися монетками запустится автоматически!
        CurrencyManager.Add(amount);

        // Или если нужна анимация с конкретной позиции:
        // CurrencyManager.AddWithAnimation(fromPosition, amount);
    }

    private void OnAnimationComplete(int amount)
    {
        Debug.Log($"💰 Получено {amount} монет! Анимация завершена.");
    }

    private void OnBalanceChanged(int newBalance) => UpdateBalanceDisplay();

    private void UpdateBalanceDisplay()
    {
        if (_balanceText != null)
            _balanceText.text = $"💰 {CurrencyManager.Balance}";
    }
}

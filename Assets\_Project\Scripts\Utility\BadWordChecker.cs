using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

[Serializable]
public class BadWordResult
{
    public string levelName;
    public int matrixIndex;
    public string badWord;
    public Vector2Int startPosition;
    public Vector2Int endPosition;
    public string direction;

    public BadWordResult(string levelName, int matrixIndex, string badWord, Vector2Int startPos, Vector2Int endPos, string direction)
    {
        this.levelName = levelName;
        this.matrixIndex = matrixIndex;
        this.badWord = badWord;
        this.startPosition = startPos;
        this.endPosition = endPos;
        this.direction = direction;
    }

    public override string ToString()
    {
        return $"[{levelName}] Matrix {matrixIndex}: '{badWord}' at ({startPosition.x},{startPosition.y}) → ({endPosition.x},{endPosition.y}) [{direction}]";
    }
}

public static class BadWordChecker
{
    private const string BANNED_DATA_PATH = "Assets/_Project/Prefabs/SO/tables/BannedData.asset";
    private const string LEVELS_CONFIG_PATH = "Assets/_Project/Data/LevelsConfigSO.asset";
    
    private static List<BadWordResult> lastResults = new List<BadWordResult>();
    
    public static List<BadWordResult> LastResults => lastResults;

    public static List<BadWordResult> CheckAllLevels()
    {
        lastResults.Clear();

#if UNITY_EDITOR
        var bannedData = UnityEditor.AssetDatabase.LoadAssetAtPath<BannedData>(BANNED_DATA_PATH);
        var levelsConfig = UnityEditor.AssetDatabase.LoadAssetAtPath<LevelsConfigSO>(LEVELS_CONFIG_PATH);

        if (bannedData == null)
        {
            Debug.LogError($"BannedData not found at path: {BANNED_DATA_PATH}");
            return lastResults;
        }

        if (levelsConfig == null)
        {
            Debug.LogError($"LevelsConfigSO not found at path: {LEVELS_CONFIG_PATH}");
            return lastResults;
        }

        foreach (LevelsSO level in levelsConfig.Levels)
        {
            CheckLevel(level, bannedData.Words);
        }
#endif

        return lastResults;
    }

    private static void CheckLevel(LevelsSO level, List<string> bannedWords)
    {
        if (level.FillMatrixes == null) return;

        for (int matrixIndex = 0; matrixIndex < level.FillMatrixes.Count; matrixIndex++)
        {
            var matches = BadWordValidationService.FindAllBadWords(level.FillMatrixes[matrixIndex], bannedWords);
            
            foreach (var match in matches)
            {
                BadWordResult result = new BadWordResult(level.name, matrixIndex, match.word, match.startPosition, match.endPosition, match.direction);
                lastResults.Add(result);
            }
        }
    }

    public static void ClearResults()
    {
        lastResults.Clear();
    }

    public static int GetTotalBadWordsCount()
    {
        return lastResults.Count;
    }

    public static void ExportResultsToConsole()
    {
        var groupedResults = lastResults.GroupBy(r => r.levelName).OrderBy(g => g.Key);
        
        foreach (var levelGroup in groupedResults)
        {
            foreach (var result in levelGroup)
            {
                Debug.LogWarning(result.ToString());
            }
        }
    }
}
